<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.OperationLogMapper">

    <!-- Base result map for OperationLog entity -->
    <resultMap id="BaseResultMap" type="com.example.demo.entity.OperationLog">
        <id column="id" property="id" />
        <result column="operation_time" property="operationTime" />
        <result column="operator" property="operator" />
        <result column="source_bank" property="sourceBank" />
        <result column="target_bank" property="targetBank" />
        <result column="operation_type" property="operationType" />
        <result column="old_value" property="oldValue" />
        <result column="new_value" property="newValue" />
        <result column="discount_rate" property="discountRate" />
        <result column="old_discount_rate" property="oldDiscountRate" />
        <result column="new_discount_rate" property="newDiscountRate" />
    </resultMap>

    <!-- Base column list -->
    <sql id="Base_Column_List">
        id, operation_time, operator, source_bank, target_bank, operation_type, old_value, new_value,
        discount_rate, old_discount_rate, new_discount_rate
    </sql>

    <!-- Find all operation logs -->
    <select id="findAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM operation_log
        ORDER BY operation_time DESC
    </select>

    <!-- Find operation logs with pagination -->
    <select id="findByPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM operation_log
        ORDER BY operation_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- Count total number of operation logs -->
    <select id="count" resultType="long">
        SELECT COUNT(*) FROM operation_log
    </select>

    <!-- Insert a new operation log -->
    <insert id="insert" parameterType="com.example.demo.entity.OperationLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO operation_log (
            operation_time, operator, source_bank, target_bank, operation_type, old_value, new_value,
            discount_rate, old_discount_rate, new_discount_rate
        ) VALUES (
            #{operationTime}, #{operator}, #{sourceBank}, #{targetBank}, #{operationType}, #{oldValue}, #{newValue},
            #{discountRate}, #{oldDiscountRate}, #{newDiscountRate}
        )
    </insert>
</mapper>
