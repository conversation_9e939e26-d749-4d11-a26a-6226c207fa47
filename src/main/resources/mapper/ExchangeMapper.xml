<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.ExchangeMapper">

    <!-- Base result map for Exchange entity -->
    <resultMap id="BaseResultMap" type="com.example.demo.entity.Exchange">
        <id column="id" property="id" />
        <result column="source_bank" property="sourceBank" />
        <result column="target_bank" property="targetBank" />
        <result column="exchange_rate" property="exchangeRate" />
        <result column="amount" property="amount" />
        <result column="update_time" property="updateTime" />
        <result column="updater" property="updater" />
    </resultMap>

    <!-- Base column list -->
    <sql id="Base_Column_List">
        id, source_bank, target_bank, exchange_rate, amount, update_time, updater
    </sql>

    <!-- Find all exchanges -->
    <select id="findAll" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            exchange
        ORDER BY
            id
    </select>

    <!-- Find exchanges with pagination -->
    <select id="findByPage" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            exchange
        ORDER BY
            id
        LIMIT #{offset}, #{limit}
    </select>

    <!-- Find exchanges with pagination and optional filters -->
    <select id="findByPageWithFilters" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            exchange
        <where>
            <if test="sourceBank != null and sourceBank != ''">
                source_bank = #{sourceBank}
            </if>
            <if test="targetBank != null and targetBank != ''">
                AND target_bank LIKE CONCAT('%', #{targetBank}, '%')
            </if>
        </where>
        ORDER BY
            id
        LIMIT #{offset}, #{limit}
    </select>

    <!-- Count total number of exchanges -->
    <select id="count" resultType="long">
        SELECT
            COUNT(*)
        FROM
            exchange
    </select>

    <!-- Count total number of exchanges with optional filters -->
    <select id="countWithFilters" resultType="long">
        SELECT
            COUNT(*)
        FROM
            exchange
        <where>
            <if test="sourceBank != null and sourceBank != ''">
                source_bank = #{sourceBank}
            </if>
            <if test="targetBank != null and targetBank != ''">
                AND target_bank LIKE CONCAT('%', #{targetBank}, '%')
            </if>
        </where>
    </select>

    <!-- Find exchange by ID -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            exchange
        WHERE
            id = #{id}
    </select>

    <!-- Insert a new exchange -->
    <insert id="insert" parameterType="com.example.demo.entity.Exchange" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO exchange (
            source_bank, target_bank, exchange_rate, amount, updater, update_time
        ) VALUES (
            #{sourceBank}, #{targetBank}, #{exchangeRate}, #{amount}, #{updater}, NOW()
        )
    </insert>

    <!-- Update exchange -->
    <update id="update" parameterType="com.example.demo.entity.Exchange">
        UPDATE exchange
        <set>
            <if test="sourceBank != null">source_bank = #{sourceBank},</if>
            <if test="targetBank != null">target_bank = #{targetBank},</if>
            <if test="exchangeRate != null">exchange_rate = #{exchangeRate},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="updater != null">updater = #{updater},</if>
            update_time = NOW(),
        </set>
        WHERE id = #{id}
    </update>

    <!-- Update exchange rate and amount -->
    <update id="updateRateAndAmount">
        UPDATE exchange
        SET
            exchange_rate = #{exchangeRate},
            amount = #{amount},
            updater = #{updater},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- Delete exchange by ID -->
    <delete id="deleteById">
        DELETE FROM exchange
        WHERE id = #{id}
    </delete>

    <!-- Get the maximum ID from the exchange table -->
    <select id="getMaxId" resultType="java.lang.Long">
        SELECT MAX(id) FROM exchange
    </select>

    <!-- Create a temporary table with resequenced IDs -->
    <update id="createTempTableWithResequencedIds">
        SET @counter = 0;

        CREATE TEMPORARY TABLE temp_exchange
        SELECT (@counter:=@counter+1) AS new_id, e.*
        FROM exchange e
        ORDER BY e.id;

        SET FOREIGN_KEY_CHECKS = 0;

        TRUNCATE TABLE exchange;

        INSERT INTO exchange (id, source_bank, target_bank, exchange_rate, amount, update_time, updater)
        SELECT
            new_id, source_bank, target_bank, exchange_rate, amount, update_time, updater
        FROM
            temp_exchange;

        SET @max_id = (SELECT MAX(id) FROM exchange);

        ALTER TABLE exchange AUTO_INCREMENT = @max_id + 1;

        DROP TEMPORARY TABLE temp_exchange;

        SET FOREIGN_KEY_CHECKS = 1;
    </update>

    <!-- Update original table with resequenced IDs (dummy method, not used) -->
    <update id="updateTableWithResequencedIds">
        SELECT 1
    </update>

    <!-- Drop the temporary table (dummy method, not used) -->
    <update id="dropTempTable">
        SELECT 1
    </update>

    <!-- Reset the auto-increment counter (dummy method, not used) -->
    <update id="resetAutoIncrement">
        SELECT 1
    </update>
</mapper>
