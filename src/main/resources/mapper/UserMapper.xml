<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.UserMapper">

    <!-- Base result map for User entity -->
    <resultMap id="BaseResultMap" type="com.example.demo.entity.User">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="email" property="email" />
        <result column="full_name" property="fullName" />
        <result column="role" property="role" />
        <result column="role_title" property="roleTitle" />
        <result column="avatar" property="avatar" />
        <result column="department" property="department" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- Base column list -->
    <sql id="Base_Column_List">
        id, username, password, email, full_name, role, role_title, avatar, department, status, create_time, update_time
    </sql>

    <!-- Find user by ID -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            users
        WHERE
            id = #{id}
    </select>

    <!-- Find user by username -->
    <select id="findByUsername" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            users
        WHERE
            username = #{username}
    </select>

    <!-- Find user by username and password -->
    <select id="findByUsernameAndPassword" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            users
        WHERE
            username = #{username} AND password = #{password}
    </select>

    <!-- Get user permissions by user ID -->
    <select id="getUserPermissions" resultType="java.lang.String">
        SELECT
            permission
        FROM
            user_permissions
        WHERE
            user_id = #{userId}
    </select>

    <!-- Insert a new user -->
    <insert id="insert" parameterType="com.example.demo.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (
            username, password, email, status
        ) VALUES (
            #{username}, #{password}, #{email}, #{status}
        )
    </insert>

    <!-- Update user -->
    <update id="update" parameterType="com.example.demo.entity.User">
        UPDATE users
        <set>
            <if test="password != null">password = #{password},</if>
            <if test="email != null">email = #{email},</if>
            <if test="status != null">status = #{status},</if>
        </set>
        WHERE id = #{id}
    </update>
</mapper>
