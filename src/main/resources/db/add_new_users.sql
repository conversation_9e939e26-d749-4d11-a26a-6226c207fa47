-- Use the database
USE db_exchange;

-- Add new users
-- 1. Head Office User
INSERT INTO `users` (`username`, `password`, `email`, `full_name`, `role`, `role_title`, `avatar`, `department`, `status`)
SELECT 'headoffice', '123456', '<EMAIL>', '总行管理员', 'headoffice', '总行', '', '总行业务部', 1
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM `users` WHERE `username` = 'headoffice');

-- 2. Branch User
INSERT INTO `users` (`username`, `password`, `email`, `full_name`, `role`, `role_title`, `avatar`, `department`, `status`)
SELECT 'branch', '123456', '<EMAIL>', '分行操作员', 'branch', '分行', '', '北京银行学XX分行', 1
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM `users` WHERE `username` = 'branch');

-- 3. Third Party User
INSERT INTO `users` (`username`, `password`, `email`, `full_name`, `role`, `role_title`, `avatar`, `department`, `status`)
SELECT 'thirdparty', '123456', '<EMAIL>', '第三方用户', 'thirdparty', '第三方', '', '合作伙伴', 1
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM `users` WHERE `username` = 'thirdparty');


INSERT INTO `users` (`username`, `password`, `email`, `full_name`, `role`, `role_title`, `avatar`, `department`, `status`)
SELECT 'subbranch', '123456', '<EMAIL>', 'XX支行用户', 'subbranch', 'XX支行', '', 'XX支行', 1
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM `users` WHERE `username` = 'subbranch');

-- Add permissions for Head Office User
INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'headoffice'), 'read'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'headoffice') 
    AND `permission` = 'read'
);

INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'headoffice'), 'write'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'headoffice') 
    AND `permission` = 'write'
);

INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'headoffice'), 'approve'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'headoffice') 
    AND `permission` = 'approve'
);

INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'headoffice'), 'view_branch'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'headoffice') 
    AND `permission` = 'view_branch'
);

-- Add permissions for Branch User
INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'branch'), 'read'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'branch') 
    AND `permission` = 'read'
);

INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'branch'), 'write'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'branch') 
    AND `permission` = 'write'
);

-- Add permissions for Third Party User
INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'thirdparty'), 'read'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'thirdparty') 
    AND `permission` = 'read'
);

-- Add permissions for Third Party User
INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT
    (SELECT `id` FROM `users` WHERE `username` = 'subbranch'), 'read'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions`
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'subbranch')
      AND `permission` = 'read'
);
