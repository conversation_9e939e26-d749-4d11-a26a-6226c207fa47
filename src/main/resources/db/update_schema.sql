CREATE DATABASE IF NOT EXISTS db_exchange;
-- 使用数据库
USE db_exchange;

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'User ID',
  `username` varchar(50) NOT NULL COMMENT 'Username',
  `password` varchar(100) NOT NULL COMMENT 'Password',
  `email` varchar(100) DEFAULT NULL COMMENT 'Email address',
  `full_name` varchar(100) DEFAULT NULL COMMENT 'Full name',
  `role` varchar(50) DEFAULT NULL COMMENT 'User role',
  `role_title` varchar(100) DEFAULT NULL COMMENT 'Role title',
  `avatar` varchar(255) DEFAULT NULL COMMENT 'User avatar URL',
  `department` varchar(100) DEFAULT NULL COMMENT 'User department',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'User status (0: inactive, 1: active)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='User table';

-- 检查是否已有数据，如果没有则插入测试用户
INSERT INTO `users` (`username`, `password`, `email`, `full_name`, `role`, `role_title`, `avatar`, `department`, `status`)
SELECT 'admin', '123456', '<EMAIL>', '系统管理员', 'admin', '管理员', '', '系统管理部', 1
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM `users` WHERE `username` = 'admin');

-- 创建用户权限表
CREATE TABLE IF NOT EXISTS `user_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Permission ID',
  `user_id` bigint NOT NULL COMMENT 'User ID',
  `permission` varchar(50) NOT NULL COMMENT 'Permission name',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='User permissions table';

-- 插入默认权限
INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'admin'), 'read'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'admin') 
    AND `permission` = 'read'
);

INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'admin'), 'write'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'admin') 
    AND `permission` = 'write'
);

INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'admin'), 'delete'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'admin') 
    AND `permission` = 'delete'
);

INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'admin'), 'approve'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'admin') 
    AND `permission` = 'approve'
);

INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'admin'), 'manage_users'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'admin') 
    AND `permission` = 'manage_users'
);

INSERT INTO `user_permissions` (`user_id`, `permission`)
SELECT 
    (SELECT `id` FROM `users` WHERE `username` = 'admin'), 'view_all'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM `user_permissions` 
    WHERE `user_id` = (SELECT `id` FROM `users` WHERE `username` = 'admin') 
    AND `permission` = 'view_all'
);
