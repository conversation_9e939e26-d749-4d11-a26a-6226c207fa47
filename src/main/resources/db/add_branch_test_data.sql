-- Use the database
USE db_exchange;

-- Add test data for branch user testing
-- These records have target banks that match the branch user's department "北京银行XX分行"
INSERT INTO `exchange` (`source_bank`, `target_bank`, `exchange_rate`, `amount`, `update_time`, `updater`)
VALUES
-- Records that should be visible to branch user (department: 北京银行XX分行)
('中国银行', '北京银行XX分行', 1.01, 1000, '2021-02-28 10:30:00', '王岳 北京银行XX分行'),
('河北银行', '北京银行XX分行', 0.99, 900, '2021-02-28 11:30:00', '李明 北京银行XX分行'),
('农业银行', '北京银行XX分行', 0.98, 800, '2021-02-28 12:30:00', '张华 北京银行XX分行'),
('建设银行', '北京银行XX分行', 1.02, 1200, '2021-03-01 09:15:00', '赵强 北京银行XX分行'),
('工商银行', '北京银行XX分行', 1.03, 1500, '2021-03-01 10:20:00', '钱芳 北京银行XX分行'),

-- Records that should NOT be visible to branch user (different target banks)
('中国银行', '上海银行XX分行', 1.01, 1000, '2021-02-28 10:30:00', '王岳 上海银行XX分行'),
('河北银行', '深圳银行XX分行', 0.99, 900, '2021-02-28 11:30:00', '李明 深圳银行XX分行'),
('农业银行', '广州银行XX分行', 0.98, 800, '2021-02-28 12:30:00', '张华 广州银行XX分行');
