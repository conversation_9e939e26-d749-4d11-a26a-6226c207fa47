-- Use the database
USE db_exchange;

-- Create exchange table
CREATE TABLE IF NOT EXISTS `exchange` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Exchange ID',
  `source_bank` varchar(100) NOT NULL COMMENT 'Source bank',
  `target_bank` varchar(100) NOT NULL COMMENT 'Target bank',
  `exchange_rate` decimal(10,2) NOT NULL COMMENT 'Exchange rate',
  `amount` int NOT NULL COMMENT 'Amount',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
  `updater` varchar(50) DEFAULT NULL COMMENT 'Updater',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Exchange table';

-- Insert sample data
INSERT INTO `exchange` (`source_bank`, `target_bank`, `exchange_rate`, `amount`, `update_time`, `updater`)
VALUES 
('中国银行', '兴业银行XX支行', 1.01, 1000, '2021-02-28 10:30:00', '王岳 兴业银行XX分行'),
('河北银行', '兴业银行XX支行', 0.99, 900, '2021-02-28 10:30:00', '王岳 兴业银行XX分行'),
('农业银行', '兴业银行XX支行', 0.99, 800, '2021-02-28 10:30:00', '王岳 兴业银行XX分行'),
('建设银行', '兴业银行XX支行', 0.98, 700, '2021-02-28 10:30:00', '王岳 兴业银行XX分行'),
('中国银行', '南京银行XX支行', 1.02, 2000, '2021-02-28 10:30:00', '刘华 南京银行XX分行'),
('河北银行', '南京银行XX支行', 1.01, 1900, '2021-02-28 10:30:00', '刘华 南京银行XX分行'),
('农业银行', '南京银行XX支行', 0.99, 1800, '2021-02-28 10:30:00', '刘华 南京银行XX分行'),
('建设银行', '南京银行XX支行', 1.00, 1700, '2021-02-28 10:30:00', '刘华 南京银行XX分行'),
('中国银行', '招商银行XX支行', 1.01, 1500, '2021-02-28 10:30:00', '冯凡 管理员'),
('河北银行', '招商银行XX支行', 0.99, 1400, '2021-02-28 10:30:00', '冯凡 管理员');
