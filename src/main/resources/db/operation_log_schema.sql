-- Use the database
USE db_exchange;

-- Create operation_log table
CREATE TABLE IF NOT EXISTS `operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Log ID',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Operation time',
  `operator` varchar(100) NOT NULL COMMENT 'Operator name',
  `source_bank` varchar(100) NOT NULL COMMENT 'Source bank',
  `target_bank` varchar(100) NOT NULL COMMENT 'Target bank',
  `operation_type` varchar(50) NOT NULL COMMENT 'Operation type (modify, delete)',
  `old_value` varchar(100) DEFAULT NULL COMMENT '贴现额度',
  `new_value` varchar(100) DEFAULT NULL COMMENT '修改后的贴现额度',
  `discount_rate` decimal(10,2) DEFAULT NULL COMMENT 'Discount Rate',
  `old_discount_rate` decimal(10,2) DEFAULT NULL COMMENT 'Old Discount Rate',
  `new_discount_rate` decimal(10,2) DEFAULT NULL COMMENT 'New Discount Rate',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Operation log table';

-- Insert sample data
INSERT INTO `operation_log` (`operation_time`, `operator`, `source_bank`, `target_bank`, `operation_type`, `old_value`, `new_value`, `discount_rate`, `old_discount_rate`, `new_discount_rate`)
VALUES
-- 修改贴现额度操作
('2021-02-28 10:30:00', '王苗', '兴业银行', '中国银行', 'modify_贴现额度', '800W', '1000W', 1.01, NULL, NULL),
('2021-02-28 10:30:00', '管理员', '兴业银行', '农业银行', 'modify_贴现额度', '800W', '1000W', 0.99, NULL, NULL),
-- 修改贴现价格操作
('2021-02-28 10:35:00', '王苗', '兴业银行', '中国银行', 'modify_贴现价格', '1.01', '1.02', NULL, 1.01, 1.02),
('2021-02-28 10:35:00', '管理员', '兴业银行', '农业银行', 'modify_贴现价格', '0.99', '1.01', NULL, 0.99, 1.01),
-- 同时修改贴现额度和贴现价格操作
('2021-02-28 10:38:00', '王苗', '兴业银行', '建设银行', 'modify_贴现额度和贴现价格', '800W', '1200W', NULL, 1.01, 1.03),
('2021-02-28 10:39:00', '管理员', '兴业银行', '工商银行', 'modify_贴现额度和贴现价格', '900W', '1500W', NULL, 0.98, 1.02),
-- 删除操作
('2021-02-28 10:40:00', '管理员', '兴业银行', '中国银行', 'delete', NULL, NULL, 1.02, NULL, NULL),
-- 创建操作
('2021-02-28 11:15:00', '王岳', '中国银行', '兴业银行XX支行', 'create', NULL, '贴现额度: 1000W', 1.01, NULL, NULL),
('2021-02-28 11:20:00', '刘华', '河北银行', '南京银行XX支行', 'create', NULL, '贴现额度: 1900W', 1.01, NULL, NULL);
