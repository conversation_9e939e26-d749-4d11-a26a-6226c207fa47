-- Resequence IDs in the exchange table
SET @counter = 0;

-- Create a temporary table with resequenced IDs
CREATE TEMPORARY TABLE temp_exchange
SELECT (@counter:=@counter+1) AS new_id, e.*
FROM exchange e
ORDER BY e.id;

-- Disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Truncate the original table
TRUNCATE TABLE exchange;

-- Insert data back with new IDs
INSERT INTO exchange (id, source_bank, target_bank, exchange_rate, amount, update_time, updater)
SELECT 
    new_id, source_bank, target_bank, exchange_rate, amount, update_time, updater
FROM 
    temp_exchange;
    
-- Reset auto-increment counter
SET @max_id = (SELECT MAX(id) FROM exchange);
ALTER TABLE exchange AUTO_INCREMENT = @max_id + 1;

-- Drop temporary table
DROP TEMPORARY TABLE temp_exchange;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
