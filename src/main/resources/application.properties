spring.application.name=demo1

# Database Configuration
spring.datasource.url=****************************************************************************************************
spring.datasource.username=root
spring.datasource.password=qwerasdf
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# MyBatis Configuration
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.example.demo.entity
mybatis.configuration.map-underscore-to-camel-case=true

# Server Configuration
server.port=8083

# Logging Configuration
logging.level.com.example.demo=DEBUG
logging.level.org.springframework=INFO
logging.level.org.mybatis=INFO
