package com.example.demo.exception;

import lombok.Getter;

/**
 * Business exception
 */
@Getter
public class BusinessException extends RuntimeException {
    
    /**
     * Error code
     */
    private final Integer code;
    
    /**
     * Error message
     */
    private final String message;
    
    /**
     * Constructor with code and message
     *
     * @param code error code
     * @param message error message
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
}
