package com.example.demo.util;

/**
 * Demo class for RSA encryption and decryption
 */
public class RSADemo {

    public static void main(String[] args) {
        // Generate RSA key pair
        System.out.println("Generating RSA key pair...");
        RSAKeyPair keyPair = RSAUtil.generateKeyPair();
        
        System.out.println("Public Key: " + keyPair.getPublicKey());
        System.out.println("Private Key: " + keyPair.getPrivateKey());
        
        // Test data
        String plainPassword = "123456";
        System.out.println("\nOriginal Password: " + plainPassword);
        
        // Encrypt password
        System.out.println("\nEncrypting password...");
        String encryptedPassword = RSAUtil.encrypt(plainPassword, keyPair.getPublicKey());
        System.out.println("Encrypted Password: " + encryptedPassword);
        
        // Decrypt password
        System.out.println("\nDecrypting password...");
        String decryptedPassword = RSAUtil.decrypt(encryptedPassword, keyPair.getPrivateKey());
        System.out.println("Decrypted Password: " + decryptedPassword);
        
        // Verify
        System.out.println("\nVerification: " + (plainPassword.equals(decryptedPassword) ? "Success" : "Failed"));
    }
}
