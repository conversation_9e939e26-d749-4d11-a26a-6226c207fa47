package com.example.demo.util;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * RSA encryption and decryption utility class
 */
@Slf4j
public class RSAUtil {

    /**
     * RSA algorithm name
     */
    private static final String RSA_ALGORITHM = "RSA";
    
    /**
     * RSA key size (512 bits is recommended for security)
     */
    private static final int KEY_SIZE = 512;

    /**
     * Generate RSA key pair
     *
     * @return RSA key pair
     */
    public static RSAKeyPair generateKeyPair() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA_ALGORITHM);
            keyPairGenerator.initialize(KEY_SIZE, new SecureRandom());
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            
            String publicKey = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
            String privateKey = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());
            
            return new RSAKeyPair(publicKey, privateKey);
        } catch (Exception e) {
            log.error("Failed to generate RSA key pair", e);
            throw new RuntimeException("Failed to generate RSA key pair", e);
        }
    }

    /**
     * Encrypt data using RSA public key
     *
     * @param data      data to encrypt
     * @param publicKey RSA public key (Base64 encoded)
     * @return encrypted data (Base64 encoded)
     */
    public static String encrypt(String data, String publicKey) {
        try {
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            PublicKey key = KeyFactory.getInstance(RSA_ALGORITHM)
                    .generatePublic(new X509EncodedKeySpec(Base64.getDecoder().decode(publicKey)));
            cipher.init(Cipher.ENCRYPT_MODE, key);
            
            byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("Failed to encrypt data with RSA", e);
            throw new RuntimeException("Failed to encrypt data with RSA", e);
        }
    }

    /**
     * Decrypt data using RSA private key
     *
     * @param encryptedData encrypted data (Base64 encoded)
     * @param privateKey    RSA private key (Base64 encoded)
     * @return decrypted data
     */
    public static String decrypt(String encryptedData, String privateKey) {
        try {
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            PrivateKey key = KeyFactory.getInstance(RSA_ALGORITHM)
                    .generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey)));
            cipher.init(Cipher.DECRYPT_MODE, key);
            
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Failed to decrypt data with RSA", e);
            throw new RuntimeException("Failed to decrypt data with RSA", e);
        }
    }
}
