package com.example.demo.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * User permission entity class
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPermission {
    
    /**
     * Permission ID
     */
    private Long id;
    
    /**
     * User ID
     */
    private Long userId;
    
    /**
     * Permission name
     */
    private String permission;
    
    /**
     * Creation time
     */
    private LocalDateTime createTime;
}
