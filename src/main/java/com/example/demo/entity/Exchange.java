package com.example.demo.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Exchange entity class
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Exchange {

    /**
     * Exchange ID
     */
    private Long id;

    /**
     * Source bank
     */
    private String sourceBank;

    /**
     * Target bank
     */
    private String targetBank;

    /**
     * Exchange rate
     */
    private BigDecimal exchangeRate;

    /**
     * Amount
     */
    private Integer amount;

    /**
     * Update time
     */
    private LocalDateTime updateTime;

    /**
     * Updater
     */
    private String updater;
}
