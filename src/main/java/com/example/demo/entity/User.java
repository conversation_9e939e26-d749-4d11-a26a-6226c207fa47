package com.example.demo.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * User entity class
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {

    /**
     * User ID
     */
    private Long id;

    /**
     * Username
     */
    private String username;

    /**
     * Password
     */
    private String password;

    /**
     * Email address
     */
    private String email;

    /**
     * Full name
     */
    private String fullName;

    /**
     * User role
     */
    private String role;

    /**
     * Role title
     */
    private String roleTitle;

    /**
     * User avatar
     */
    private String avatar;

    /**
     * User department
     */
    private String department;

    /**
     * User permissions
     */
    private List<String> permissions;

    /**
     * User status (0: inactive, 1: active)
     */
    private Integer status;

    /**
     * Creation time
     */
    private LocalDateTime createTime;

    /**
     * Last update time
     */
    private LocalDateTime updateTime;
}
