package com.example.demo.mapper;

import com.example.demo.entity.OperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Operation log mapper interface
 */
@Mapper
public interface OperationLogMapper {

    /**
     * Find all operation logs
     *
     * @return list of operation logs
     */
    List<OperationLog> findAll();

    /**
     * Find operation logs with pagination
     *
     * @param offset offset
     * @param limit limit
     * @return list of operation logs
     */
    List<OperationLog> findByPage(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * Count total number of operation logs
     *
     * @return total count
     */
    long count();

    /**
     * Insert a new operation log
     *
     * @param operationLog operation log entity
     * @return affected rows
     */
    int insert(OperationLog operationLog);
}
