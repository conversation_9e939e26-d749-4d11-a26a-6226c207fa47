package com.example.demo.mapper;

import com.example.demo.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * User mapper interface
 */
@Mapper
public interface UserMapper {

    /**
     * Find user by ID
     *
     * @param id user ID
     * @return user entity
     */
    User findById(@Param("id") Long id);

    /**
     * Find user by username
     *
     * @param username username
     * @return user entity
     */
    User findByUsername(@Param("username") String username);

    /**
     * Find user by username and password
     *
     * @param username username
     * @param password password
     * @return user entity
     */
    User findByUsernameAndPassword(@Param("username") String username, @Param("password") String password);

    /**
     * Get user permissions by user ID
     *
     * @param userId user ID
     * @return list of permissions
     */
    List<String> getUserPermissions(@Param("userId") Long userId);

    /**
     * Insert a new user
     *
     * @param user user entity
     * @return affected rows
     */
    int insert(User user);

    /**
     * Update user
     *
     * @param user user entity
     * @return affected rows
     */
    int update(User user);
}
