package com.example.demo.mapper;

import com.example.demo.entity.Exchange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * Exchange mapper interface
 */
@Mapper
public interface ExchangeMapper {

    /**
     * Find all exchanges
     *
     * @return list of exchanges
     */
    List<Exchange> findAll();

    /**
     * Find exchanges with pagination
     *
     * @param offset offset
     * @param limit limit
     * @return list of exchanges
     */
    List<Exchange> findByPage(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * Find exchanges with pagination and optional filters
     *
     * @param offset offset
     * @param limit limit
     * @param sourceBank source bank (optional) - exact match
     * @param targetBank target bank (optional) - fuzzy search (contains)
     * @return list of exchanges
     */
    List<Exchange> findByPageWithFilters(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("sourceBank") String sourceBank,
            @Param("targetBank") String targetBank);

    /**
     * Count total number of exchanges
     *
     * @return total count
     */
    long count();

    /**
     * Count total number of exchanges with optional filters
     *
     * @param sourceBank source bank (optional) - exact match
     * @param targetBank target bank (optional) - fuzzy search (contains)
     * @return total count
     */
    long countWithFilters(
            @Param("sourceBank") String sourceBank,
            @Param("targetBank") String targetBank);

    /**
     * Find exchange by ID
     *
     * @param id exchange ID
     * @return exchange entity
     */
    Exchange findById(@Param("id") Long id);

    /**
     * Insert a new exchange
     *
     * @param exchange exchange entity
     * @return affected rows
     */
    int insert(Exchange exchange);

    /**
     * Update exchange
     *
     * @param exchange exchange entity
     * @return affected rows
     */
    int update(Exchange exchange);

    /**
     * Update exchange rate and amount
     *
     * @param id exchange ID
     * @param exchangeRate exchange rate
     * @param amount amount
     * @param updater updater
     * @return affected rows
     */
    int updateRateAndAmount(
            @Param("id") Long id,
            @Param("exchangeRate") BigDecimal exchangeRate,
            @Param("amount") Integer amount,
            @Param("updater") String updater);

    /**
     * Delete exchange by ID
     *
     * @param id exchange ID
     * @return affected rows
     */
    int deleteById(@Param("id") Long id);

    /**
     * Get the maximum ID from the exchange table
     *
     * @return maximum ID
     */
    Long getMaxId();

    /**
     * Create a temporary table with resequenced IDs
     */
    void createTempTableWithResequencedIds();

    /**
     * Update original table with resequenced IDs
     */
    void updateTableWithResequencedIds();

    /**
     * Drop the temporary table
     */
    void dropTempTable();

    /**
     * Reset the auto-increment counter
     *
     * @param value new auto-increment value
     */
    void resetAutoIncrement(@Param("value") Long value);
}
