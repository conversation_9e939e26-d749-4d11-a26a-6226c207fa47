package com.example.demo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Batch exchange update request DTO for updating multiple exchanges
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchExchangeUpdateRequest {
    
    /**
     * List of exchanges to update
     */
    private List<ExchangeUpdateItem> exchanges;
    
    /**
     * Updater
     */
    private String updater;
    
    /**
     * Exchange update item for batch updates
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExchangeUpdateItem {
        
        /**
         * Exchange ID
         */
        private Long id;
        
        /**
         * Exchange rate
         */
        private BigDecimal exchangeRate;
        
        /**
         * Amount
         */
        private Integer amount;
    }
}
