package com.example.demo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Generic pagination response wrapper
 *
 * @param <T> type of data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {
    
    /**
     * Current page number (1-based)
     */
    private Integer pageNum;
    
    /**
     * Page size
     */
    private Integer pageSize;
    
    /**
     * Total number of records
     */
    private Long total;
    
    /**
     * Total number of pages
     */
    private Integer pages;
    
    /**
     * List of data
     */
    private List<T> list;
    
    /**
     * Whether there is a previous page
     */
    private Boolean hasPrevious;
    
    /**
     * Whether there is a next page
     */
    private Boolean hasNext;
    
    /**
     * Create a page response
     *
     * @param pageNum current page number
     * @param pageSize page size
     * @param total total number of records
     * @param list list of data
     * @param <T> type of data
     * @return page response
     */
    public static <T> PageResponse<T> of(Integer pageNum, Integer pageSize, Long total, List<T> list) {
        int pages = (int) Math.ceil((double) total / pageSize);
        return PageResponse.<T>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .total(total)
                .pages(pages)
                .list(list)
                .hasPrevious(pageNum > 1)
                .hasNext(pageNum < pages)
                .build();
    }
}
