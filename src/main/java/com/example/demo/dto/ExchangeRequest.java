package com.example.demo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Exchange request DTO for create and update operations
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeRequest {
    
    /**
     * Source bank
     */
    private String sourceBank;
    
    /**
     * Target bank
     */
    private String targetBank;
    
    /**
     * Exchange rate
     */
    private BigDecimal exchangeRate;
    
    /**
     * Amount
     */
    private Integer amount;
    
    /**
     * Updater
     */
    private String updater;
}
