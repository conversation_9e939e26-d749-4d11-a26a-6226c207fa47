package com.example.demo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Operation log response DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperationLogResponse {

    /**
     * Log ID
     */
    private Long id;

    /**
     * Operation time
     */
    private LocalDateTime operationTime;

    /**
     * Operator name
     */
    private String operator;

    /**
     * Source bank
     */
    private String sourceBank;

    /**
     * Target bank
     */
    private String targetBank;

    /**
     * Operation type (modify, delete)
     */
    private String operationType;

    /**
     * 贴现额度
     */
    private String oldValue;

    /**
     * 修改后的贴现额度
     */
    private String newValue;

    /**
     * 贴现价格
     */
    private BigDecimal discountRate;

    /**
     * 修改前贴现价格
     */
    private BigDecimal oldDiscountRate;

    /**
     * 修改后贴现价格
     */
    private BigDecimal newDiscountRate;
}
