package com.example.demo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Exchange response DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeResponse {
    
    /**
     * Exchange ID
     */
    private Long id;
    
    /**
     * Source bank
     */
    private String sourceBank;
    
    /**
     * Target bank
     */
    private String targetBank;
    
    /**
     * Exchange rate
     */
    private BigDecimal exchangeRate;
    
    /**
     * Amount
     */
    private Integer amount;
    
    /**
     * Update time
     */
    private LocalDateTime updateTime;
    
    /**
     * Updater
     */
    private String updater;
}
