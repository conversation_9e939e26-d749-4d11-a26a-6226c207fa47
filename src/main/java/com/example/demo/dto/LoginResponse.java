package com.example.demo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Login response DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginResponse {

    /**
     * User ID
     */
    private String id;

    /**
     * Username
     */
    private String username;

    /**
     * Password
     */
    private String password;

    /**
     * Email address
     */
    private String email;

    /**
     * Full name
     */
    private String fullName;

    /**
     * User role
     */
    private String role;

    /**
     * Role title
     */
    private String roleTitle;

    /**
     * User permissions
     */
    private List<String> permissions;

    /**
     * User avatar
     */
    private String avatar;

    /**
     * User department
     */
    private String department;

    /**
     * Token (could be used for JWT in a real application)
     */
    private String token;
}
