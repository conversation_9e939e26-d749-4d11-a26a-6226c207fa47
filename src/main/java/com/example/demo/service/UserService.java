package com.example.demo.service;

import com.example.demo.dto.LoginRequest;
import com.example.demo.dto.LoginResponse;
import com.example.demo.entity.User;

/**
 * User service interface
 */
public interface UserService {

    /**
     * Login with username and password
     *
     * @param loginRequest login request
     * @return login response
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * Find user by username
     *
     * @param username username
     * @return user entity
     */
    User findByUsername(String username);

    /**
     * Register a new user with encrypted password
     *
     * @param user user entity with plain password
     * @return registered user with encrypted password
     */
    User register(User user);

    /**
     * Update user password
     *
     * @param userId      user ID
     * @param newPassword new plain password
     * @return true if password updated successfully, false otherwise
     */
    boolean updatePassword(Long userId, String newPassword);
}
