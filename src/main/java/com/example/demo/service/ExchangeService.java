package com.example.demo.service;

import com.example.demo.dto.BatchExchangeUpdateRequest;
import com.example.demo.dto.ExchangeRequest;
import com.example.demo.dto.ExchangeResponse;
import com.example.demo.dto.PageResponse;

import java.util.List;

/**
 * Exchange service interface
 */
public interface ExchangeService {

    /**
     * Get all exchanges
     *
     * @return list of exchange responses
     */
    List<ExchangeResponse> getAllExchanges();

    /**
     * Get exchanges with pagination
     *
     * @param pageNum page number (1-based)
     * @param pageSize page size
     * @return page response of exchange responses
     */
    PageResponse<ExchangeResponse> getExchangesByPage(int pageNum, int pageSize);

    /**
     * Get exchanges with pagination and optional filters
     *
     * @param pageNum page number (1-based)
     * @param pageSize page size
     * @param sourceBank source bank (optional, 开户行) - exact match
     * @param targetBank target bank (optional, 贴现行) - fuzzy search (contains)
     * @return page response of exchange responses
     */
    PageResponse<ExchangeResponse> getExchangesByPageWithFilters(int pageNum, int pageSize, String sourceBank, String targetBank);

    /**
     * Get exchange by ID
     *
     * @param id exchange ID
     * @return exchange response
     */
    ExchangeResponse getExchangeById(Long id);

    /**
     * Create a new exchange
     *
     * @param exchangeRequest exchange request
     * @return created exchange response
     */
    ExchangeResponse createExchange(ExchangeRequest exchangeRequest);

    /**
     * Update exchange
     *
     * @param id exchange ID
     * @param exchangeRequest exchange request
     * @return updated exchange response
     */
    ExchangeResponse updateExchange(Long id, ExchangeRequest exchangeRequest);

    /**
     * Batch update exchanges (only exchangeRate and amount)
     *
     * @param batchRequest batch update request
     * @return list of updated exchange responses
     */
    List<ExchangeResponse> batchUpdateExchanges(BatchExchangeUpdateRequest batchRequest);

    /**
     * Delete exchange by ID
     *
     * @param id exchange ID
     * @return true if deleted successfully
     */
    boolean deleteExchange(Long id);

    /**
     * Delete exchange by ID and resequence IDs
     *
     * @param id exchange ID
     * @return true if deleted and resequenced successfully
     */
    boolean deleteExchangeAndResequence(Long id);
}
