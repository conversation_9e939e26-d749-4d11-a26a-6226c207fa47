package com.example.demo.service;

/**
 * Password service interface
 */
public interface PasswordService {

    /**
     * Encrypt password
     *
     * @param plainPassword plain password
     * @return encrypted password
     */
    String encryptPassword(String plainPassword);

    /**
     * Verify password
     *
     * @param plainPassword   plain password
     * @param encryptedPassword encrypted password
     * @return true if password matches, false otherwise
     */
    boolean verifyPassword(String plainPassword, String encryptedPassword);
}
