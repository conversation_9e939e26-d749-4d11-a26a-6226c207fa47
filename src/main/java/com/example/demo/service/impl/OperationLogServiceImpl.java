package com.example.demo.service.impl;

import com.example.demo.dto.OperationLogResponse;
import com.example.demo.dto.PageResponse;
import com.example.demo.entity.OperationLog;
import com.example.demo.mapper.OperationLogMapper;
import com.example.demo.service.OperationLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Operation log service implementation
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationLogServiceImpl implements OperationLogService {

    private final OperationLogMapper operationLogMapper;

    @Override
    public List<OperationLogResponse> getAllOperationLogs() {
        log.info("Getting all operation logs");
        List<OperationLog> logs = operationLogMapper.findAll();
        return logs.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public PageResponse<OperationLogResponse> getOperationLogsByPage(int pageNum, int pageSize) {
        log.info("Getting operation logs by page: pageNum={}, pageSize={}", pageNum, pageSize);

        // Calculate offset
        int offset = (pageNum - 1) * pageSize;

        // Get logs for the current page
        List<OperationLog> logs = operationLogMapper.findByPage(offset, pageSize);
        List<OperationLogResponse> logResponses = logs.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        // Get total count
        long total = operationLogMapper.count();

        // Calculate total pages
        int totalPages = (int) Math.ceil((double) total / pageSize);

        return PageResponse.<OperationLogResponse>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .total(total)
                .pages(totalPages)
                .list(logResponses)
                .hasPrevious(pageNum > 1)
                .hasNext(pageNum < totalPages)
                .build();
    }

    @Override
    public OperationLogResponse logCreateOperation(String operator, String sourceBank, String targetBank, String value) {
        log.info("Logging create operation: operator={}, sourceBank={}, targetBank={}, value={}",
                operator, sourceBank, targetBank, value);

        OperationLog operationLog = new OperationLog();
        operationLog.setOperationTime(LocalDateTime.now());
        operationLog.setOperator(operator);
        operationLog.setSourceBank(sourceBank);
        operationLog.setTargetBank(targetBank);
        operationLog.setOperationType("create");
        // 提取贴现额度信息
        if (value != null && value.contains("贴现额度:")) {
            try {
                int amountIndex = value.indexOf("贴现额度:") + "贴现额度:".length();
                String amountStr = value.substring(amountIndex).trim();
                if (amountStr.contains(",")) {
                    amountStr = amountStr.substring(0, amountStr.indexOf(","));
                }
                // 只记录贴现额度
                operationLog.setNewValue(amountStr);
            } catch (Exception e) {
                log.warn("Failed to parse discount amount from value: {}", value);
                operationLog.setNewValue(value);
            }
        } else {
            operationLog.setNewValue(value);
        }

        // 尝试从值中提取贴现价格
        if (value != null && value.contains("贴现价格:")) {
            try {
                int priceIndex = value.indexOf("贴现价格:") + "贴现价格:".length();
                String priceStr = value.substring(priceIndex).trim();
                if (priceStr.contains(",")) {
                    priceStr = priceStr.substring(0, priceStr.indexOf(","));
                }
                BigDecimal discountRate = new BigDecimal(priceStr);
                operationLog.setDiscountRate(discountRate);
            } catch (Exception e) {
                log.warn("Failed to parse discount rate from value: {}", value);
            }
        }

        int rows = operationLogMapper.insert(operationLog);
        if (rows > 0) {
            log.info("Operation log created with ID: {}", operationLog.getId());
            return convertToResponse(operationLog);
        } else {
            log.error("Failed to create operation log");
            return null;
        }
    }

    // 查询操作不需要记录日志

    @Override
    public OperationLogResponse logModifyOperation(String operator, String sourceBank, String targetBank, String oldValue, String newValue, String fieldName) {
        log.info("Logging modify operation: operator={}, sourceBank={}, targetBank={}, fieldName={}, oldValue={}, newValue={}",
                operator, sourceBank, targetBank, fieldName, oldValue, newValue);

        String operationType = "modify";
        if (fieldName != null && !fieldName.isEmpty()) {
            operationType = "modify_" + fieldName;
        }

        OperationLog operationLog = new OperationLog();
        operationLog.setOperationTime(LocalDateTime.now());
        operationLog.setOperator(operator);
        operationLog.setSourceBank(sourceBank);
        operationLog.setTargetBank(targetBank);
        operationLog.setOperationType(operationType);
        operationLog.setOldValue(oldValue);
        operationLog.setNewValue(newValue);

        // 如果是修改贴现价格，则记录到专门的字段中
        if ("贴现价格".equals(fieldName)) {
            try {
                BigDecimal oldRate = new BigDecimal(oldValue);
                BigDecimal newRate = new BigDecimal(newValue);
                operationLog.setOldDiscountRate(oldRate);
                operationLog.setNewDiscountRate(newRate);
            } catch (Exception e) {
                log.warn("Failed to parse discount rate values: {}, {}", oldValue, newValue);
            }
        }

        int rows = operationLogMapper.insert(operationLog);
        if (rows > 0) {
            log.info("Operation log created with ID: {}", operationLog.getId());
            return convertToResponse(operationLog);
        } else {
            log.error("Failed to create operation log");
            return null;
        }
    }

    @Override
    public OperationLogResponse logDeleteOperation(String operator, String sourceBank, String targetBank) {
        log.info("Logging delete operation: operator={}, sourceBank={}, targetBank={}",
                operator, sourceBank, targetBank);

        OperationLog operationLog = new OperationLog();
        operationLog.setOperationTime(LocalDateTime.now());
        operationLog.setOperator(operator);
        operationLog.setSourceBank(sourceBank);
        operationLog.setTargetBank(targetBank);
        operationLog.setOperationType("delete");
        // 删除操作需要记录被删除记录的贴现额度
        try {
            // 这里可以通过 ExchangeMapper 查询被删除的记录，获取其贴现额度
            // 由于我们没有直接访问 ExchangeMapper 的权限，这里只是示例
            // 实际实现时，应该在 ExchangeServiceImpl 中传入贴现额度
            operationLog.setOldValue(sourceBank + "-" + targetBank + " 的记录已删除");
        } catch (Exception e) {
            log.warn("Failed to get discount amount for deleted record");
        }

        int rows = operationLogMapper.insert(operationLog);
        if (rows > 0) {
            log.info("Operation log created with ID: {}", operationLog.getId());
            return convertToResponse(operationLog);
        } else {
            log.error("Failed to create operation log");
            return null;
        }
    }

    @Override
    public OperationLogResponse insertOperationLog(OperationLog operationLog) {
        log.info("Inserting operation log directly: type={}, operator={}, sourceBank={}, targetBank={}",
                operationLog.getOperationType(), operationLog.getOperator(),
                operationLog.getSourceBank(), operationLog.getTargetBank());

        int rows = operationLogMapper.insert(operationLog);
        if (rows > 0) {
            log.info("Operation log created with ID: {}", operationLog.getId());
            return convertToResponse(operationLog);
        } else {
            log.error("Failed to create operation log");
            return null;
        }
    }

    /**
     * Convert entity to response DTO
     *
     * @param operationLog operation log entity
     * @return operation log response DTO
     */
    private OperationLogResponse convertToResponse(OperationLog operationLog) {
        return OperationLogResponse.builder()
                .id(operationLog.getId())
                .operationTime(operationLog.getOperationTime())
                .operator(operationLog.getOperator())
                .sourceBank(operationLog.getSourceBank())
                .targetBank(operationLog.getTargetBank())
                .operationType(operationLog.getOperationType())
                .oldValue(operationLog.getOldValue())
                .newValue(operationLog.getNewValue())
                .discountRate(operationLog.getDiscountRate())
                .oldDiscountRate(operationLog.getOldDiscountRate())
                .newDiscountRate(operationLog.getNewDiscountRate())
                .build();
    }
}
