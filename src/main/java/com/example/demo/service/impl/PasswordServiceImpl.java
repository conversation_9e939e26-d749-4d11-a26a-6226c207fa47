package com.example.demo.service.impl;

import com.example.demo.service.PasswordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Password service implementation
 * Simple implementation without encryption
 */
@Slf4j
@Service
public class PasswordServiceImpl implements PasswordService {

    public PasswordServiceImpl() {
        log.info("PasswordService initialized without encryption");
    }

    @Override
    public String encryptPassword(String plainPassword) {
        if (plainPassword == null || plainPassword.isEmpty()) {
            throw new IllegalArgumentException("Password cannot be null or empty");
        }

        // No encryption, just return the plain password
        return plainPassword;
    }

    @Override
    public boolean verifyPassword(String plainPassword, String storedPassword) {
        if (plainPassword == null || plainPassword.isEmpty() || storedPassword == null || storedPassword.isEmpty()) {
            return false;
        }

        // Simple string comparison
        return plainPassword.equals(storedPassword);
    }
}
