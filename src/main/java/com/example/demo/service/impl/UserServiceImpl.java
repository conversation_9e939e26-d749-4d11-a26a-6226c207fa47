package com.example.demo.service.impl;

import com.example.demo.dto.LoginRequest;
import com.example.demo.dto.LoginResponse;
import com.example.demo.entity.User;
import com.example.demo.exception.BusinessException;
import com.example.demo.mapper.UserMapper;
import com.example.demo.service.PasswordService;
import com.example.demo.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * User service implementation
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final PasswordService passwordService;

    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        log.info("User login attempt: {}", loginRequest.getUsername());

        // Find user by username
        User user = userMapper.findByUsername(loginRequest.getUsername());

        // Verify password using RSA decryption
        if (user == null || !passwordService.verifyPassword(loginRequest.getPassword(), user.getPassword())) {
            log.warn("Login failed for user: {}", loginRequest.getUsername());
            throw new BusinessException(1001, "Invalid username or password");
        }


        String token = UUID.randomUUID().toString().replace("-", "");

        log.info("User logged in successfully: {}", loginRequest.getUsername());

        // 从数据库获取用户权限
        List<String> permissions = userMapper.getUserPermissions(user.getId());

        return LoginResponse.builder()
                .id(user.getId().toString())
                .username(user.getUsername())
                .password(user.getPassword())
                .email(user.getEmail())
                .fullName(user.getFullName())
                .role(user.getRole())
                .roleTitle(user.getRoleTitle())
                .permissions(permissions)
                .avatar(user.getAvatar())
                .department(user.getDepartment())
                .token(token)
                .build();
    }

    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public User register(User user) {
        log.info("Registering new user: {}", user.getUsername());

        // Check if username already exists
        User existingUser = userMapper.findByUsername(user.getUsername());
        if (existingUser != null) {
            log.warn("Username already exists: {}", user.getUsername());
            throw new BusinessException(1002, "Username already exists");
        }

        // Encrypt password using RSA
        String encryptedPassword = passwordService.encryptPassword(user.getPassword());
        user.setPassword(encryptedPassword);

        // Insert user with encrypted password
        int rows = userMapper.insert(user);
        if (rows <= 0) {
            log.error("Failed to register user: {}", user.getUsername());
            throw new BusinessException(1003, "Failed to register user");
        }

        log.info("User registered successfully: {}", user.getUsername());
        return user;
    }

    @Override
    public boolean updatePassword(Long userId, String newPassword) {
        log.info("Updating password for user ID: {}", userId);

        // Find user by ID
        User user = userMapper.findById(userId);
        if (user == null) {
            log.warn("User not found with ID: {}", userId);
            return false;
        }

        // Encrypt new password
        String encryptedPassword = passwordService.encryptPassword(newPassword);
        user.setPassword(encryptedPassword);

        // Update user password
        int rows = userMapper.update(user);

        boolean success = rows > 0;
        if (success) {
            log.info("Password updated successfully for user ID: {}", userId);
        } else {
            log.error("Failed to update password for user ID: {}", userId);
        }

        return success;
    }
}
