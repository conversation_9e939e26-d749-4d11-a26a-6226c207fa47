package com.example.demo.service.impl;

import com.example.demo.dto.BatchExchangeUpdateRequest;
import com.example.demo.dto.ExchangeRequest;
import com.example.demo.dto.ExchangeResponse;
import com.example.demo.dto.PageResponse;
import com.example.demo.entity.Exchange;
import com.example.demo.entity.OperationLog;
import com.example.demo.entity.User;
import com.example.demo.exception.BusinessException;
import com.example.demo.mapper.ExchangeMapper;
import com.example.demo.service.ExchangeService;
import com.example.demo.service.OperationLogService;
import com.example.demo.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Exchange service implementation
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExchangeServiceImpl implements ExchangeService {

    private final ExchangeMapper exchangeMapper;
    private final JdbcTemplate jdbcTemplate;
    private final OperationLogService operationLogService;
    private final UserService userService;

    @Override
    public List<ExchangeResponse> getAllExchanges() {
        log.info("Getting all exchanges");

        List<Exchange> exchanges = exchangeMapper.findAll();
        return exchanges.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public PageResponse<ExchangeResponse> getExchangesByPage(int pageNum, int pageSize) {
        log.info("Getting exchanges by page: pageNum={}, pageSize={}", pageNum, pageSize);

        // Validate page parameters
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        // Calculate offset
        int offset = (pageNum - 1) * pageSize;

        // Get total count
        long total = exchangeMapper.count();

        // Get data for current page
        List<Exchange> exchanges = exchangeMapper.findByPage(offset, pageSize);

        // Convert to response DTOs
        List<ExchangeResponse> responseList = exchanges.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        // Create page response
        return PageResponse.of(pageNum, pageSize, total, responseList);
    }

    @Override
    public PageResponse<ExchangeResponse> getExchangesByPageWithFilters(int pageNum, int pageSize, String sourceBank, String targetBank) {
        log.info("Getting exchanges by page with filters: pageNum={}, pageSize={}, sourceBank={}, targetBank={}",
                pageNum, pageSize, sourceBank, targetBank);

        // Validate page parameters
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        // Calculate offset
        int offset = (pageNum - 1) * pageSize;

        // Get total count with filters
        long total = exchangeMapper.countWithFilters(sourceBank, targetBank);

        // Get data for current page with filters
        List<Exchange> exchanges = exchangeMapper.findByPageWithFilters(offset, pageSize, sourceBank, targetBank);

        // Convert to response DTOs
        List<ExchangeResponse> responseList = exchanges.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        // Create page response
        return PageResponse.of(pageNum, pageSize, total, responseList);
    }

    @Override
    public PageResponse<ExchangeResponse> getExchangesByPageForCurrentUser(int pageNum, int pageSize, String sourceBank, String targetBank, String currentUserDepartment) {
        log.info("Getting exchanges by page for current user: pageNum={}, pageSize={}, sourceBank={}, targetBank={}, userDepartment={}",
                pageNum, pageSize, sourceBank, targetBank, currentUserDepartment);

        // Validate page parameters
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        // Calculate offset
        int offset = (pageNum - 1) * pageSize;

        // Get total count with filters for current user
        long total = exchangeMapper.countWithFiltersForCurrentUser(sourceBank, targetBank, currentUserDepartment);

        // Get data for current page with filters for current user
        List<Exchange> exchanges = exchangeMapper.findByPageForCurrentUser(offset, pageSize, sourceBank, targetBank, currentUserDepartment);

        // Convert to response DTOs
        List<ExchangeResponse> responseList = exchanges.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        // Create page response
        return PageResponse.of(pageNum, pageSize, total, responseList);
    }

    @Override
    public PageResponse<ExchangeResponse> getExchangesByPageWithUserRole(int pageNum, int pageSize, String sourceBank, String targetBank, String currentUser) {
        log.info("Getting exchanges by page with user role: pageNum={}, pageSize={}, sourceBank={}, targetBank={}, currentUser={}",
                pageNum, pageSize, sourceBank, targetBank, currentUser);

        // Get user information to determine role
        User user = userService.findByUsername(currentUser);
        if (user == null) {
            log.warn("User not found: {}", currentUser);
            throw new BusinessException(1001, "User not found");
        }

        String userRole = user.getRole();
        log.info("User role: {}", userRole);

        // Validate page parameters
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        PageResponse<ExchangeResponse> pageResponse;

        if ("admin".equals(userRole) || "headoffice".equals(userRole)) {
            // Admin and headoffice users can see all data
            log.info("Admin/headoffice user - showing all data");
            if ((sourceBank == null || sourceBank.isEmpty()) && (targetBank == null || targetBank.isEmpty())) {
                pageResponse = getExchangesByPage(pageNum, pageSize);
            } else {
                pageResponse = getExchangesByPageWithFilters(pageNum, pageSize, sourceBank, targetBank);
            }
        } else if ("branch".equals(userRole)) {
            // Branch users can only see their own branch data
            log.info("Branch user - filtering by department: {}", user.getDepartment());
            pageResponse = getExchangesByPageForCurrentUser(pageNum, pageSize, sourceBank, targetBank, user.getDepartment());
        } else {
            // Other users (like thirdparty) get limited access
            log.info("Limited access user - showing filtered data");
            if ((sourceBank == null || sourceBank.isEmpty()) && (targetBank == null || targetBank.isEmpty())) {
                pageResponse = getExchangesByPage(pageNum, pageSize);
            } else {
                pageResponse = getExchangesByPageWithFilters(pageNum, pageSize, sourceBank, targetBank);
            }
        }

        return pageResponse;
    }

    @Override
    public ExchangeResponse getExchangeById(Long id) {
        log.info("Getting exchange by ID: {}", id);

        Exchange exchange = exchangeMapper.findById(id);
        if (exchange == null) {
            log.warn("Exchange not found with ID: {}", id);
            throw new BusinessException(2001, "Exchange not found");
        }

        return convertToResponse(exchange);
    }

    @Override
    public ExchangeResponse createExchange(ExchangeRequest exchangeRequest) {
        log.info("Creating new exchange");
        Exchange exchange = convertToEntity(exchangeRequest);

        // 记录创建操作 - 贴现额度
        operationLogService.logCreateOperation(
                exchange.getUpdater(),
                exchange.getSourceBank(),
                exchange.getTargetBank(),
                "贴现额度: " + exchange.getAmount() + "W, 贴现价格: " + exchange.getExchangeRate()
        );

        int rows = exchangeMapper.insert(exchange);
        if (rows <= 0) {
            log.error("Failed to create exchange");
            throw new BusinessException(2002, "Failed to create exchange");
        }
        log.info("Exchange created with ID: {}", exchange.getId());
        return convertToResponse(exchange);
    }

    @Override
    public ExchangeResponse updateExchange(Long id, ExchangeRequest exchangeRequest) {
        log.info("Updating exchange with ID: {}", id);
        Exchange existingExchange = exchangeMapper.findById(id);
        if (existingExchange == null) {
            log.warn("Exchange not found with ID: {}", id);
            throw new BusinessException(2001, "Exchange not found");
        }

        Exchange exchange = convertToEntity(exchangeRequest);
        exchange.setId(id);

        // 检查是否同时修改了贴现额度和贴现价格
        boolean amountChanged = !existingExchange.getAmount().equals(exchange.getAmount());
        boolean rateChanged = !existingExchange.getExchangeRate().equals(exchange.getExchangeRate());

        if (amountChanged || rateChanged) {
            String oldAmountValue = existingExchange.getAmount() + "W";
            String newAmountValue = exchange.getAmount() + "W";

            BigDecimal oldRateValue = existingExchange.getExchangeRate();
            BigDecimal newRateValue = exchange.getExchangeRate();

            String operationType = "modify";
            if (amountChanged && rateChanged) {
                operationType = "modify_贴现额度和贴现价格";
            } else if (amountChanged) {
                operationType = "modify_贴现额度";
            } else {
                operationType = "modify_贴现价格";
            }

            // 创建一条包含所有修改的日志记录
            OperationLog operationLog = new OperationLog();
            operationLog.setOperationTime(LocalDateTime.now());
            operationLog.setOperator(exchange.getUpdater());
            operationLog.setSourceBank(exchange.getSourceBank());
            operationLog.setTargetBank(exchange.getTargetBank());
            operationLog.setOperationType(operationType);

            // 设置贴现额度
            operationLog.setOldValue(oldAmountValue);
            operationLog.setNewValue(newAmountValue);

            // 设置贴现价格
            if (rateChanged) {
                operationLog.setOldDiscountRate(oldRateValue);
                operationLog.setNewDiscountRate(newRateValue);
            }

            // 插入日志记录
            operationLogService.insertOperationLog(operationLog);
        }

        int rows = exchangeMapper.update(exchange);
        if (rows <= 0) {
            log.error("Failed to update exchange with ID: {}", id);
            throw new BusinessException(2003, "Failed to update exchange");
        }

        log.info("Exchange updated with ID: {}", id);
        return getExchangeById(id);
    }

    @Override
    public boolean deleteExchange(Long id) {
        log.info("Deleting exchange with ID: {}", id);
        Exchange existingExchange = exchangeMapper.findById(id);
        if (existingExchange == null) {
            log.warn("Exchange not found with ID: {}", id);
            throw new BusinessException(2001, "Exchange not found");
        }

        // Log the deletion operation
        operationLogService.logDeleteOperation(
                existingExchange.getUpdater(),
                existingExchange.getSourceBank(),
                existingExchange.getTargetBank()
        );

        // 在这里可以修改 OperationLogService 接口和实现，添加一个新方法，接收贴现额度参数
        // 但为了保持兼容性，我们暂时不修改接口

        int rows = exchangeMapper.deleteById(id);
        if (rows <= 0) {
            log.error("Failed to delete exchange with ID: {}", id);
            throw new BusinessException(2004, "Failed to delete exchange");
        }

        log.info("Exchange deleted with ID: {}", id);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteExchangeAndResequence(Long id) {
        log.info("Deleting exchange with ID: {} and resequencing IDs", id);

        // First delete the exchange
        boolean deleted = deleteExchange(id);
        if (!deleted) {
            return false;
        }

        try {
            // Resequence IDs using SQL script
            log.info("Starting ID resequencing");

            // Get all IDs in order
            List<Long> ids = jdbcTemplate.queryForList("SELECT id FROM exchange ORDER BY id", Long.class);
            log.info("Current IDs: {}", ids);

            // Start with ID 1
            int newId = 1;

            // Update each ID
            for (Long oldId : ids) {
                jdbcTemplate.update("UPDATE exchange SET id = ? WHERE id = ?", newId + 1000000, oldId);
                newId++;
            }

            // Update IDs to their final values
            jdbcTemplate.update("UPDATE exchange SET id = id - 1000000");

            // Reset auto-increment
            jdbcTemplate.update("ALTER TABLE exchange AUTO_INCREMENT = ?", newId);

            log.info("ID resequencing completed successfully");
            return true;
        } catch (Exception e) {
            log.error("Error during ID resequencing: {}", e.getMessage(), e);
            throw new BusinessException(2005, "Failed to resequence IDs: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ExchangeResponse> batchUpdateExchanges(BatchExchangeUpdateRequest batchRequest) {
        log.info("Batch updating exchanges, count: {}",
                batchRequest.getExchanges() != null ? batchRequest.getExchanges().size() : 0);

        if (batchRequest.getExchanges() == null || batchRequest.getExchanges().isEmpty()) {
            log.warn("No exchanges to update in batch request");
            return new ArrayList<>();
        }

        List<ExchangeResponse> updatedExchanges = new ArrayList<>();
        String updater = batchRequest.getUpdater();

        // Validate updater
        if (updater == null || updater.trim().isEmpty()) {
            log.warn("Updater is required for batch updates");
            updater = "System"; // Default updater if not provided
        }

        for (BatchExchangeUpdateRequest.ExchangeUpdateItem item : batchRequest.getExchanges()) {
            Long id = item.getId();

            // Check if exchange exists
            Exchange existingExchange = exchangeMapper.findById(id);
            if (existingExchange == null) {
                log.warn("Exchange not found with ID: {}, skipping in batch update", id);
                continue;
            }

            // Log the values being updated
            log.info("Updating exchange ID: {}, exchangeRate: {}, amount: {}, updater: {}",
                    id, item.getExchangeRate(), item.getAmount(), updater);

            // Validate the values
            if (item.getExchangeRate() == null || item.getAmount() == null) {
                log.warn("Exchange rate and amount are required for ID: {}, skipping", id);
                continue;
            }

            // 检查是否同时修改了贴现额度和贴现价格
            boolean amountChanged = !existingExchange.getAmount().equals(item.getAmount());
            boolean rateChanged = !existingExchange.getExchangeRate().equals(item.getExchangeRate());

            if (amountChanged || rateChanged) {
                String oldAmountValue = existingExchange.getAmount() + "W";
                String newAmountValue = item.getAmount() + "W";

                BigDecimal oldRateValue = existingExchange.getExchangeRate();
                BigDecimal newRateValue = item.getExchangeRate();

                String operationType = "modify";
                if (amountChanged && rateChanged) {
                    operationType = "modify_贴现额度和贴现价格";
                } else if (amountChanged) {
                    operationType = "modify_贴现额度";
                } else {
                    operationType = "modify_贴现价格";
                }

                // 创建一条包含所有修改的日志记录
                OperationLog operationLog = new OperationLog();
                operationLog.setOperationTime(LocalDateTime.now());
                operationLog.setOperator(updater);
                operationLog.setSourceBank(existingExchange.getSourceBank());
                operationLog.setTargetBank(existingExchange.getTargetBank());
                operationLog.setOperationType(operationType);

                // 设置贴现额度
                operationLog.setOldValue(oldAmountValue);
                operationLog.setNewValue(newAmountValue);

                // 设置贴现价格
                if (rateChanged) {
                    operationLog.setOldDiscountRate(oldRateValue);
                    operationLog.setNewDiscountRate(newRateValue);
                }

                // 插入日志记录
                operationLogService.insertOperationLog(operationLog);
            }

            // Update only exchangeRate and amount
            int rows = exchangeMapper.updateRateAndAmount(
                    id,
                    item.getExchangeRate(),
                    item.getAmount(),
                    updater);

            log.info("Update result for ID {}: {} rows affected", id, rows);

            if (rows <= 0) {
                log.error("Failed to update exchange with ID: {} in batch update", id);
                continue;
            }

            // Get updated exchange
            Exchange updatedExchange = exchangeMapper.findById(id);
            if (updatedExchange != null) {
                updatedExchanges.add(convertToResponse(updatedExchange));
                log.info("Exchange updated with ID: {} in batch update", id);
            } else {
                log.error("Failed to retrieve updated exchange with ID: {} after update", id);
            }
        }

        log.info("Batch update completed, successfully updated: {}", updatedExchanges.size());
        return updatedExchanges;
    }

    /**
     * Convert Exchange entity to ExchangeResponse DTO
     *
     * @param exchange exchange entity
     * @return exchange response DTO
     */
    private ExchangeResponse convertToResponse(Exchange exchange) {
        return ExchangeResponse.builder()
                .id(exchange.getId())
                .sourceBank(exchange.getSourceBank())
                .targetBank(exchange.getTargetBank())
                .exchangeRate(exchange.getExchangeRate())
                .amount(exchange.getAmount())
                .updateTime(exchange.getUpdateTime())
                .updater(exchange.getUpdater())
                .build();
    }

    /**
     * Convert ExchangeRequest DTO to Exchange entity
     *
     * @param request exchange request DTO
     * @return exchange entity
     */
    private Exchange convertToEntity(ExchangeRequest request) {
        Exchange exchange = new Exchange();
        exchange.setSourceBank(request.getSourceBank());
        exchange.setTargetBank(request.getTargetBank());
        exchange.setExchangeRate(request.getExchangeRate());
        exchange.setAmount(request.getAmount());
        exchange.setUpdater(request.getUpdater());
        exchange.setUpdateTime(LocalDateTime.now()); // 设置当前时间
        return exchange;
    }
}
