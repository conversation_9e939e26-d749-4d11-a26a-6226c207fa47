package com.example.demo.service;

import com.example.demo.dto.OperationLogResponse;
import com.example.demo.dto.PageResponse;
import com.example.demo.entity.OperationLog;

import java.util.List;

/**
 * Operation log service interface
 */
public interface OperationLogService {

    /**
     * Get all operation logs
     *
     * @return list of operation logs
     */
    List<OperationLogResponse> getAllOperationLogs();

    /**
     * Get operation logs with pagination
     *
     * @param pageNum page number (1-based)
     * @param pageSize page size
     * @return paginated list of operation logs
     */
    PageResponse<OperationLogResponse> getOperationLogsByPage(int pageNum, int pageSize);

    /**
     * Log a creation operation
     *
     * @param operator operator name
     * @param sourceBank source bank
     * @param targetBank target bank
     * @param value created value
     * @return operation log response
     */
    OperationLogResponse logCreateOperation(String operator, String sourceBank, String targetBank, String value);

    // 查询操作不需要记录日志

    /**
     * Log a modification operation
     *
     * @param operator operator name
     * @param sourceBank source bank
     * @param targetBank target bank
     * @param oldValue old value
     * @param newValue new value
     * @param fieldName field name being modified (optional)
     * @return operation log response
     */
    OperationLogResponse logModifyOperation(String operator, String sourceBank, String targetBank, String oldValue, String newValue, String fieldName);

    /**
     * Log a deletion operation
     *
     * @param operator operator name
     * @param sourceBank source bank
     * @param targetBank target bank
     * @return operation log response
     */
    OperationLogResponse logDeleteOperation(String operator, String sourceBank, String targetBank);

    /**
     * Insert an operation log directly
     *
     * @param operationLog operation log entity
     * @return operation log response
     */
    OperationLogResponse insertOperationLog(OperationLog operationLog);
}
