package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.LoginRequest;
import com.example.demo.dto.LoginResponse;
import com.example.demo.entity.User;
import com.example.demo.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * User controller
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * Login endpoint
     *
     * @param loginRequest login request
     * @return login response
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@RequestBody LoginRequest loginRequest) {
        log.info("Login request received for user: {}", loginRequest.getUsername());
        LoginResponse loginResponse = userService.login(loginRequest);
        return ApiResponse.success("Login successful", loginResponse);
    }

    /**
     * Get current user information by username
     *
     * @param username username
     * @return user information
     */
    @GetMapping("/current/{username}")
    public ApiResponse<User> getCurrentUser(@PathVariable String username) {
        log.info("Request to get current user info for: {}", username);
        User user = userService.findByUsername(username);
        if (user == null) {
            return ApiResponse.error(1001, "User not found");
        }
        // Remove password from response for security
        user.setPassword(null);
        return ApiResponse.success("User information retrieved successfully", user);
    }
}
