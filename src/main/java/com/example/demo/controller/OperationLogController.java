package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.OperationLogResponse;
import com.example.demo.dto.PageResponse;
import com.example.demo.service.OperationLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Operation log controller
 */
@Slf4j
@RestController
@RequestMapping("/api/logs")
@RequiredArgsConstructor
public class OperationLogController {

    private final OperationLogService operationLogService;

    /**
     * Get all operation logs
     *
     * @return list of operation logs
     */
    @GetMapping
    public ApiResponse<List<OperationLogResponse>> getAllOperationLogs() {
        log.info("Request to get all operation logs");
        List<OperationLogResponse> logs = operationLogService.getAllOperationLogs();
        return ApiResponse.success("Operation logs retrieved successfully", logs);
    }

    /**
     * Get operation logs with pagination
     *
     * @param pageNum page number (1-based, default: 1)
     * @param pageSize page size (default: 10)
     * @return paginated list of operation logs
     */
    @GetMapping("/list")
    public ApiResponse<PageResponse<OperationLogResponse>> getOperationLogsByPage(
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        log.info("Request to get operation logs by page: pageNum={}, pageSize={}", pageNum, pageSize);
        PageResponse<OperationLogResponse> pageResponse = operationLogService.getOperationLogsByPage(pageNum, pageSize);
        return ApiResponse.success("Operation logs retrieved successfully", pageResponse);
    }
}
