package com.example.demo.controller;

import com.example.demo.dto.ApiResponse;
import com.example.demo.dto.BatchExchangeUpdateRequest;
import com.example.demo.dto.ExchangeRequest;
import com.example.demo.dto.ExchangeResponse;
import com.example.demo.dto.PageResponse;
import com.example.demo.service.ExchangeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * Exchange controller
 */
@Slf4j
@RestController
@RequestMapping("/api/exchanges")
@RequiredArgsConstructor
public class ExchangeController {

    private final ExchangeService exchangeService;

    /**
     * Get all exchanges
     *
     * @return list of exchanges
     */
    @GetMapping
    public ApiResponse<List<ExchangeResponse>> getAllExchanges() {
        log.info("Request to get all exchanges");
        List<ExchangeResponse> exchanges = exchangeService.getAllExchanges();
        return ApiResponse.success("Exchanges retrieved successfully", exchanges);
    }

    /**
     * Get exchanges with pagination and optional filters
     *
     * @param pageNum page number (1-based, default: 1)
     * @param pageSize page size (default: 10)
     * @param sourceBank source bank (optional, 开户行) - exact match
     * @param targetBank target bank (optional, 贴现行) - fuzzy search (contains)
     * @return paginated list of exchanges
     */
    @GetMapping("/list")
    public ApiResponse<PageResponse<ExchangeResponse>> getExchangesByPage(
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "sourceBank", required = false) String sourceBank,
            @RequestParam(value = "targetBank", required = false) String targetBank) {
        log.info("Request to get exchanges by page: pageNum={}, pageSize={}, sourceBank={}, targetBank={}",
                pageNum, pageSize, sourceBank, targetBank);

        PageResponse<ExchangeResponse> pageResponse;

        // If both filters are null or empty, use the original method
        if ((sourceBank == null || sourceBank.isEmpty()) && (targetBank == null || targetBank.isEmpty())) {
            pageResponse = exchangeService.getExchangesByPage(pageNum, pageSize);
        } else {
            // Otherwise use the filtered method
            pageResponse = exchangeService.getExchangesByPageWithFilters(pageNum, pageSize, sourceBank, targetBank);
        }

        return ApiResponse.success("Exchanges retrieved successfully", pageResponse);
    }

    /**
     * Get exchange by ID
     *
     * @param id exchange ID
     * @return exchange response
     */
    @GetMapping("/{id}")
    public ApiResponse<ExchangeResponse> getExchangeById(@PathVariable Long id) {
        log.info("Request to get exchange with ID: {}", id);
        ExchangeResponse exchange = exchangeService.getExchangeById(id);
        return ApiResponse.success("Exchange retrieved successfully", exchange);
    }

    /**
     * Create a new exchange
     *
     * @param exchangeRequest exchange request
     * @return created exchange response
     */
    @PostMapping
    public ApiResponse<ExchangeResponse> createExchange(@RequestBody ExchangeRequest exchangeRequest) {
        log.info("Request to create a new exchange");
        ExchangeResponse createdExchange = exchangeService.createExchange(exchangeRequest);
        return ApiResponse.success("Exchange created successfully", createdExchange);
    }

    /**
     * Update exchange
     *
     * @param id exchange ID
     * @param exchangeRequest exchange request
     * @return updated exchange response
     */
    @PutMapping("/{id}")
    public ApiResponse<ExchangeResponse> updateExchange(
            @PathVariable Long id,
            @RequestBody ExchangeRequest exchangeRequest) {
        log.info("Request to update exchange with ID: {}", id);
        ExchangeResponse updatedExchange = exchangeService.updateExchange(id, exchangeRequest);
        return ApiResponse.success("Exchange updated successfully", updatedExchange);
    }

    /**
     * Batch update exchanges (only exchangeRate and amount)
     *
     * @param batchRequest batch update request
     * @return list of updated exchange responses
     */
    @PutMapping("/batch")
    public ApiResponse<List<ExchangeResponse>> batchUpdateExchanges(
            @RequestBody BatchExchangeUpdateRequest batchRequest) {
        log.info("Request to batch update exchanges, count: {}",
                batchRequest.getExchanges() != null ? batchRequest.getExchanges().size() : 0);

        // Log the request details
        if (batchRequest.getExchanges() != null) {
            for (BatchExchangeUpdateRequest.ExchangeUpdateItem item : batchRequest.getExchanges()) {
                log.info("Batch update item - ID: {}, Rate: {}, Amount: {}",
                        item.getId(), item.getExchangeRate(), item.getAmount());
            }
        }

        List<ExchangeResponse> updatedExchanges = exchangeService.batchUpdateExchanges(batchRequest);

        // Log the response
        log.info("Batch update completed, updated count: {}", updatedExchanges.size());

        return ApiResponse.success("Exchanges batch updated successfully", updatedExchanges);
    }

    /**
     * Delete exchange by ID
     *
     * @param id exchange ID
     * @return success response
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteExchange(
            @PathVariable Long id,
            @RequestParam(value = "resequence", defaultValue = "true") boolean resequence) {
        log.info("Request to delete exchange with ID: {}, resequence: {}", id, resequence);

        try {
            if (resequence) {
                boolean result = exchangeService.deleteExchangeAndResequence(id);
                log.info("Delete with resequence result: {}", result);
                return ApiResponse.success("Exchange deleted and IDs resequenced successfully", null);
            } else {
                boolean result = exchangeService.deleteExchange(id);
                log.info("Delete result: {}", result);
                return ApiResponse.success("Exchange deleted successfully", null);
            }
        } catch (Exception e) {
            log.error("Error deleting exchange: {}", e.getMessage(), e);
            return ApiResponse.error(9999, "Error deleting exchange: " + e.getMessage());
        }
    }

    /**
     * Test update endpoint to verify database connectivity
     *
     * @param id exchange ID
     * @return updated exchange
     */
    @PutMapping("/test/{id}")
    public ApiResponse<ExchangeResponse> testUpdate(@PathVariable Long id) {
        log.info("Test update for exchange with ID: {}", id);

        // Create a simple update request
        ExchangeRequest request = new ExchangeRequest();
        request.setExchangeRate(new BigDecimal("1.05"));
        request.setAmount(1200);
        request.setUpdater("Test Update");

        ExchangeResponse updatedExchange = exchangeService.updateExchange(id, request);
        return ApiResponse.success("Test update successful", updatedExchange);
    }
}
