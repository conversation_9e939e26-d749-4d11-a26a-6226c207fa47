package com.example.demo.service;

import com.example.demo.service.impl.PasswordServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for PasswordService
 */
class PasswordServiceTest {

    @InjectMocks
    private PasswordServiceImpl passwordService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Initialize password service
        passwordService = new PasswordServiceImpl();
    }

    @Test
    void testEncryptPassword() {
        // Test data
        String plainPassword = "123456";

        // Encrypt password (now just returns the plain password)
        String storedPassword = passwordService.encryptPassword(plainPassword);

        // Verify stored password is not null or empty
        assertNotNull(storedPassword);
        assertFalse(storedPassword.isEmpty());

        // Verify stored password is the same as plain password (no encryption)
        assertEquals(plainPassword, storedPassword);
    }

    @Test
    void testVerifyPassword() {
        // Test data
        String plainPassword = "123456";

        // Store password
        String storedPassword = passwordService.encryptPassword(plainPassword);

        // Verify password
        boolean result = passwordService.verifyPassword(plainPassword, storedPassword);

        // Verify result is true
        assertTrue(result);
    }

    @Test
    void testVerifyPasswordWithIncorrectPassword() {
        // Test data
        String plainPassword = "123456";
        String incorrectPassword = "654321";

        // Store password
        String storedPassword = passwordService.encryptPassword(plainPassword);

        // Verify password with incorrect password
        boolean result = passwordService.verifyPassword(incorrectPassword, storedPassword);

        // Verify result is false
        assertFalse(result);
    }

    @Test
    void testVerifyPasswordWithInvalidStoredPassword() {
        // Test data
        String plainPassword = "123456";
        String invalidStoredPassword = "";

        // Verify password with invalid stored password
        boolean result = passwordService.verifyPassword(plainPassword, invalidStoredPassword);

        // Verify result is false
        assertFalse(result);
    }

    @Test
    void testEncryptPasswordWithEmptyPassword() {
        // Test with empty password
        assertThrows(IllegalArgumentException.class, () -> {
            passwordService.encryptPassword("");
        });

        // Test with null password
        assertThrows(IllegalArgumentException.class, () -> {
            passwordService.encryptPassword(null);
        });
    }

    @Test
    void testVerifyPasswordWithInvalidEncryptedPassword() {
        // Test data
        String plainPassword = "123456";
        String invalidEncryptedPassword = "invalid_encrypted_password";

        // Verify password with invalid encrypted password
        boolean result = passwordService.verifyPassword(plainPassword, invalidEncryptedPassword);

        // Verify result is false (should be false because the passwords don't match)
        assertFalse(result);
    }
}
