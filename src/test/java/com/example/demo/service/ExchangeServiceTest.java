package com.example.demo.service;

import com.example.demo.dto.ExchangeRequest;
import com.example.demo.dto.ExchangeResponse;
import com.example.demo.dto.PageResponse;
import com.example.demo.entity.Exchange;
import com.example.demo.exception.BusinessException;
import com.example.demo.mapper.ExchangeMapper;
import com.example.demo.service.impl.ExchangeServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class ExchangeServiceTest {

    @Mock
    private ExchangeMapper exchangeMapper;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private OperationLogService operationLogService;

    @InjectMocks
    private ExchangeServiceImpl exchangeService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getAllExchanges() {
        // Prepare test data
        Exchange exchange1 = new Exchange();
        exchange1.setId(1L);
        exchange1.setSourceBank("中国银行");
        exchange1.setTargetBank("兴业银行XX支行");
        exchange1.setExchangeRate(new BigDecimal("1.01"));
        exchange1.setAmount(1000);
        exchange1.setUpdateTime(LocalDateTime.now());
        exchange1.setUpdater("王岳 兴业银行XX分行");

        Exchange exchange2 = new Exchange();
        exchange2.setId(2L);
        exchange2.setSourceBank("河北银行");
        exchange2.setTargetBank("兴业银行XX支行");
        exchange2.setExchangeRate(new BigDecimal("0.99"));
        exchange2.setAmount(900);
        exchange2.setUpdateTime(LocalDateTime.now());
        exchange2.setUpdater("王岳 兴业银行XX分行");

        // Mock repository response
        when(exchangeMapper.findAll()).thenReturn(Arrays.asList(exchange1, exchange2));

        // Call service method
        List<ExchangeResponse> responses = exchangeService.getAllExchanges();

        // Verify result
        assertNotNull(responses);
        assertEquals(2, responses.size());
        assertEquals(1L, responses.get(0).getId());
        assertEquals("中国银行", responses.get(0).getSourceBank());
        assertEquals("兴业银行XX支行", responses.get(0).getTargetBank());
        assertEquals(0, new BigDecimal("1.01").compareTo(responses.get(0).getExchangeRate()));
        assertEquals(1000, responses.get(0).getAmount());
        assertEquals("王岳 兴业银行XX分行", responses.get(0).getUpdater());
    }

    @Test
    void getExchangesByPage() {
        // Prepare test data
        Exchange exchange1 = new Exchange();
        exchange1.setId(1L);
        exchange1.setSourceBank("中国银行");
        exchange1.setTargetBank("兴业银行XX支行");
        exchange1.setExchangeRate(new BigDecimal("1.01"));
        exchange1.setAmount(1000);
        exchange1.setUpdateTime(LocalDateTime.now());
        exchange1.setUpdater("王岳 兴业银行XX分行");

        Exchange exchange2 = new Exchange();
        exchange2.setId(2L);
        exchange2.setSourceBank("河北银行");
        exchange2.setTargetBank("兴业银行XX支行");
        exchange2.setExchangeRate(new BigDecimal("0.99"));
        exchange2.setAmount(900);
        exchange2.setUpdateTime(LocalDateTime.now());
        exchange2.setUpdater("王岳 兴业银行XX分行");

        // Mock repository response
        when(exchangeMapper.count()).thenReturn(100L);
        when(exchangeMapper.findByPage(0, 10)).thenReturn(Arrays.asList(exchange1, exchange2));

        // Call service method
        PageResponse<ExchangeResponse> pageResponse = exchangeService.getExchangesByPage(1, 10);

        // Verify result
        assertNotNull(pageResponse);
        assertEquals(1, pageResponse.getPageNum());
        assertEquals(10, pageResponse.getPageSize());
        assertEquals(100L, pageResponse.getTotal());
        assertEquals(10, pageResponse.getPages());
        assertFalse(pageResponse.getHasPrevious());
        assertTrue(pageResponse.getHasNext());

        List<ExchangeResponse> responses = pageResponse.getList();
        assertNotNull(responses);
        assertEquals(2, responses.size());
        assertEquals(1L, responses.get(0).getId());
        assertEquals("中国银行", responses.get(0).getSourceBank());
        assertEquals("兴业银行XX支行", responses.get(0).getTargetBank());
        assertEquals(0, new BigDecimal("1.01").compareTo(responses.get(0).getExchangeRate()));
        assertEquals(1000, responses.get(0).getAmount());
        assertEquals("王岳 兴业银行XX分行", responses.get(0).getUpdater());
    }

    @Test
    void getExchangeById_Success() {
        // Prepare test data
        Exchange exchange = new Exchange();
        exchange.setId(1L);
        exchange.setSourceBank("中国银行");
        exchange.setTargetBank("兴业银行XX支行");
        exchange.setExchangeRate(new BigDecimal("1.01"));
        exchange.setAmount(1000);
        exchange.setUpdateTime(LocalDateTime.now());
        exchange.setUpdater("王岳 兴业银行XX分行");

        // Mock repository response
        when(exchangeMapper.findById(1L)).thenReturn(exchange);

        // Call service method
        ExchangeResponse response = exchangeService.getExchangeById(1L);

        // Verify result
        assertNotNull(response);
        assertEquals(1L, response.getId());
        assertEquals("中国银行", response.getSourceBank());
        assertEquals("兴业银行XX支行", response.getTargetBank());
        assertEquals(0, new BigDecimal("1.01").compareTo(response.getExchangeRate()));
        assertEquals(1000, response.getAmount());
        assertEquals("王岳 兴业银行XX分行", response.getUpdater());
    }

    @Test
    void getExchangeById_NotFound() {
        // Mock repository response
        when(exchangeMapper.findById(999L)).thenReturn(null);

        // Call service method and verify exception
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            exchangeService.getExchangeById(999L);
        });

        assertEquals(2001, exception.getCode());
        assertEquals("Exchange not found", exception.getMessage());
    }

    @Test
    void createExchange_Success() {
        // Prepare test data
        ExchangeRequest request = new ExchangeRequest();
        request.setSourceBank("中国银行");
        request.setTargetBank("兴业银行XX支行");
        request.setExchangeRate(new BigDecimal("1.01"));
        request.setAmount(1000);
        request.setUpdater("王岳 兴业银行XX分行");

        // Mock repository behavior
        doAnswer(invocation -> {
            Exchange exchange = invocation.getArgument(0);
            exchange.setId(1L);
            exchange.setUpdateTime(LocalDateTime.now());
            return 1;
        }).when(exchangeMapper).insert(any(Exchange.class));

        // Mock findById for the response
        doAnswer(invocation -> {
            Long id = invocation.getArgument(0);
            Exchange exchange = new Exchange();
            exchange.setId(id);
            exchange.setSourceBank("中国银行");
            exchange.setTargetBank("兴业银行XX支行");
            exchange.setExchangeRate(new BigDecimal("1.01"));
            exchange.setAmount(1000);
            exchange.setUpdateTime(LocalDateTime.now());
            exchange.setUpdater("王岳 兴业银行XX分行");
            return exchange;
        }).when(exchangeMapper).findById(1L);

        // Call service method
        ExchangeResponse response = exchangeService.createExchange(request);

        // Verify result
        assertNotNull(response);
        assertEquals(1L, response.getId());
        assertEquals("中国银行", response.getSourceBank());
        assertEquals("兴业银行XX支行", response.getTargetBank());
        assertEquals(0, new BigDecimal("1.01").compareTo(response.getExchangeRate()));
        assertEquals(1000, response.getAmount());
        assertEquals("王岳 兴业银行XX分行", response.getUpdater());

        // Verify repository was called
        verify(exchangeMapper, times(1)).insert(any(Exchange.class));
    }
}
