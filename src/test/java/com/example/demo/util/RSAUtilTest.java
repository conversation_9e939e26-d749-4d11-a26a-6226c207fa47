package com.example.demo.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for RSAUtil
 */
class RSAUtilTest {

    @Test
    void testGenerateKeyPair() {
        // Generate RSA key pair
        RSAKeyPair keyPair = RSAUtil.generateKeyPair();
        
        // Verify key pair is not null
        assertNotNull(keyPair);
        assertNotNull(keyPair.getPublicKey());
        assertNotNull(keyPair.getPrivateKey());
        
        // Verify keys are not empty
        assertFalse(keyPair.getPublicKey().isEmpty());
        assertFalse(keyPair.getPrivateKey().isEmpty());
    }
    
    @Test
    void testEncryptAndDecrypt() {
        // Generate RSA key pair
        RSAKeyPair keyPair = RSAUtil.generateKeyPair();
        
        // Test data
        String plainText = "123456";
        
        // Encrypt data
        String encryptedText = RSAUtil.encrypt(plainText, keyPair.getPublicKey());
        
        // Verify encrypted text is not null or empty
        assertNotNull(encryptedText);
        assertFalse(encryptedText.isEmpty());
        
        // Verify encrypted text is different from plain text
        assertNotEquals(plainText, encryptedText);
        
        // Decrypt data
        String decryptedText = RSAUtil.decrypt(encryptedText, keyPair.getPrivateKey());
        
        // Verify decrypted text matches original plain text
        assertEquals(plainText, decryptedText);
    }
    
    @Test
    void testMultipleEncryptions() {
        // Generate RSA key pair
        RSAKeyPair keyPair = RSAUtil.generateKeyPair();
        
        // Test data
        String plainText = "123456";
        
        // Encrypt data multiple times
        String encryptedText1 = RSAUtil.encrypt(plainText, keyPair.getPublicKey());
        String encryptedText2 = RSAUtil.encrypt(plainText, keyPair.getPublicKey());
        
        // Verify each encryption produces different ciphertext (due to padding)
        assertNotEquals(encryptedText1, encryptedText2);
        
        // Decrypt both encrypted texts
        String decryptedText1 = RSAUtil.decrypt(encryptedText1, keyPair.getPrivateKey());
        String decryptedText2 = RSAUtil.decrypt(encryptedText2, keyPair.getPrivateKey());
        
        // Verify both decrypted texts match original plain text
        assertEquals(plainText, decryptedText1);
        assertEquals(plainText, decryptedText2);
    }
}
