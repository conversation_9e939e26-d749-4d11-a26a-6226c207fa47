# Branch User Guide - 分行用户使用指南

## 概述

本系统现在支持分行用户只查看与自己分行相关的贴现行信息。分行用户登录后，只能看到贴现行（targetBank）包含自己部门名称的交易记录。

## 功能说明

### 1. 用户角色和权限

- **admin**: 系统管理员，可以查看所有数据
- **headoffice**: 总行用户，可以查看所有分行数据
- **branch**: 分行用户，只能查看与自己分行相关的数据
- **thirdparty**: 第三方用户，只读权限

### 2. 分行用户数据过滤规则

当分行用户使用新的API接口时，系统会：

1. 获取当前用户的部门信息（如："北京银行XX分行"）
2. 只返回贴现行（targetBank）包含该部门名称的交易记录
3. 支持额外的过滤条件（如开户行过滤）

## API 使用方法

### 获取当前用户信息

```bash
GET /api/users/current/{username}
```

示例：
```bash
curl -X GET "http://localhost:8083/api/users/current/branch"
```

### 获取分行用户的贴现行数据

```bash
GET /api/exchanges/list/current-user?currentUserDepartment={部门名称}
```

示例：
```bash
# 基本查询
curl -X GET "http://localhost:8083/api/exchanges/list/current-user?pageNum=1&pageSize=10&currentUserDepartment=北京银行XX分行"

# 带开户行过滤的查询
curl -X GET "http://localhost:8083/api/exchanges/list/current-user?pageNum=1&pageSize=10&sourceBank=中国银行&currentUserDepartment=北京银行XX分行"
```

## 测试用户

| 用户名 | 密码 | 角色 | 部门 |
|--------|------|------|------|
| branch | 123456 | branch | 北京银行XX分行 |

## 测试数据

系统中包含以下测试数据：

1. **分行相关数据**（branch用户可见）：
   - 中国银行 → 北京银行XX分行
   - 河北银行 → 北京银行XX分行

2. **其他分行数据**（branch用户不可见）：
   - 农业银行 → 上海银行XX分行
   - 其他各种银行组合

## 验证方法

1. 使用普通API接口查看所有数据：
   ```bash
   curl -X GET "http://localhost:8083/api/exchanges/list?pageNum=1&pageSize=5"
   ```

2. 使用分行用户API接口查看过滤后的数据：
   ```bash
   curl -X GET "http://localhost:8083/api/exchanges/list/current-user?pageNum=1&pageSize=10&currentUserDepartment=北京银行XX分行"
   ```

3. 对比两个接口的返回结果，确认过滤功能正常工作。

## 注意事项

1. 中文参数需要进行URL编码
2. `currentUserDepartment` 参数是必需的
3. 过滤是基于模糊匹配（LIKE）实现的
4. 支持分页和其他过滤条件的组合使用
