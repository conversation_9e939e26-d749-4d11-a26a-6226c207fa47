# Branch User Guide - 分行用户使用指南

## 概述

本系统现在支持基于用户角色的数据访问控制。不同角色的用户登录后，可以看到不同范围的贴现行信息和贴现价格：

- **admin用户**：可以显示所有的贴现行和贴现价格
- **branch用户**：只显示自己的贴现价格（贴现行包含自己部门名称的交易记录）

## 功能说明

### 1. 用户角色和权限

- **admin**: 系统管理员，可以查看所有贴现行和贴现价格
- **headoffice**: 总行用户，可以查看所有贴现行和贴现价格
- **branch**: 分行用户，只能查看与自己分行相关的贴现价格
- **thirdparty**: 第三方用户，可以查看所有数据（只读权限）

### 2. 数据过滤规则

**修改后的 `/api/exchanges/list` 接口**现在根据用户角色自动过滤数据：

1. **admin和headoffice用户**：显示所有贴现行和贴现价格
2. **branch用户**：只显示贴现行（targetBank）包含自己部门名称的交易记录
3. **其他用户**：显示所有数据（有限访问权限）

系统会：
1. 根据传入的`currentUser`参数获取用户信息
2. 检查用户角色和部门信息
3. 自动应用相应的数据过滤规则
4. 支持额外的过滤条件（如开户行过滤）

## API 使用方法

### 获取当前用户信息

```bash
GET /api/users/current/{username}
```

示例：
```bash
curl -X GET "http://localhost:8083/api/users/current/branch"
```

### 获取基于角色的贴现行数据（主要接口）

```bash
GET /api/exchanges/list?currentUser={用户名}
```

示例：
```bash
# admin用户 - 查看所有数据
curl -X GET "http://localhost:8083/api/exchanges/list?pageNum=1&pageSize=10&currentUser=admin"

# branch用户 - 只查看自己分行的数据
curl -X GET "http://localhost:8083/api/exchanges/list?pageNum=1&pageSize=10&currentUser=branch"

# 带开户行过滤的查询
curl -X GET "http://localhost:8083/api/exchanges/list?pageNum=1&pageSize=10&sourceBank=中国银行&currentUser=branch"

# headoffice用户 - 查看所有数据
curl -X GET "http://localhost:8083/api/exchanges/list?pageNum=1&pageSize=10&currentUser=headoffice"
```

### 获取分行用户的贴现行数据（备用接口）

```bash
GET /api/exchanges/list/current-user?currentUserDepartment={部门名称}
```

示例：
```bash
# 基本查询
curl -X GET "http://localhost:8083/api/exchanges/list/current-user?pageNum=1&pageSize=10&currentUserDepartment=北京银行XX分行"

# 带开户行过滤的查询
curl -X GET "http://localhost:8083/api/exchanges/list/current-user?pageNum=1&pageSize=10&sourceBank=中国银行&currentUserDepartment=北京银行XX分行"
```

## 测试用户

| 用户名 | 密码 | 角色 | 部门 |
|--------|------|------|------|
| branch | 123456 | branch | 北京银行XX分行 |

## 测试数据

系统中包含以下测试数据：

1. **分行相关数据**（branch用户可见）：
   - 中国银行 → 北京银行XX分行
   - 河北银行 → 北京银行XX分行

2. **其他分行数据**（branch用户不可见）：
   - 农业银行 → 上海银行XX分行
   - 其他各种银行组合

## 验证方法

1. **测试admin用户**（应该看到所有数据）：
   ```bash
   curl -X GET "http://localhost:8083/api/exchanges/list?pageNum=1&pageSize=5&currentUser=admin"
   ```

2. **测试branch用户**（只看到自己分行的数据）：
   ```bash
   curl -X GET "http://localhost:8083/api/exchanges/list?pageNum=1&pageSize=10&currentUser=branch"
   ```

3. **测试带过滤条件的branch用户查询**：
   ```bash
   curl -X GET "http://localhost:8083/api/exchanges/list?pageNum=1&pageSize=10&sourceBank=中国银行&currentUser=branch"
   ```

4. **测试headoffice用户**（应该看到所有数据）：
   ```bash
   curl -X GET "http://localhost:8083/api/exchanges/list?pageNum=1&pageSize=5&currentUser=headoffice"
   ```

5. **对比结果**：
   - admin和headoffice用户应该看到相同的全量数据（106条记录）
   - branch用户只能看到贴现行为"北京银行XX分行"的数据（2条记录）

## 注意事项

1. **必需参数**：`currentUser` 参数是必需的，用于确定用户角色
2. **中文参数**：中文参数需要进行URL编码
3. **过滤逻辑**：branch用户的过滤是基于模糊匹配（LIKE）实现的
4. **组合使用**：支持分页和其他过滤条件的组合使用
5. **向后兼容**：保留了原有的 `/api/exchanges/list/current-user` 接口作为备用
6. **错误处理**：如果用户不存在，系统会返回相应的错误信息
