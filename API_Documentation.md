# API Documentation

## General Information

### Base URL
```
http://localhost:8080
```

### Authentication
Authentication is handled through the login endpoint which returns a token. This token should be included in subsequent requests.

### Response Format
All API responses (except for the health check endpoint) follow a standard format:

```json
{
  "code": 0,
  "message": "",
  "data": {}
}
```

### Error Handling
The API uses the following error codes:

| Code | Description |
|------|-------------|
| 0    | Success     |
| 1001 | Invalid username or password |
| 2001 | Exchange not found |
| 2002 | Failed to create exchange |
| 2003 | Failed to update exchange |
| 2004 | Failed to delete exchange |
| 9999 | System error |

## User API

### Login

Authenticates a user and returns user information with a token.

**URL**: `/api/users/login`

**Method**: `POST`

**Request Body**:
```json
{
  "username": "string",
  "password": "string"
}
```

**Success Response**:
```json
{
  "code": 0,
  "message": "Login successful",
  "data": {
    "id": "string",
    "username": "string",
    "password": "string",
    "email": "string",
    "fullName": "string",
    "role": "string",
    "roleTitle": "string",
    "permissions": ["string"],
    "avatar": "string",
    "department": "string",
    "token": "string"
  }
}
```

**Error Response**:
```json
{
  "code": 1001,
  "message": "Invalid username or password",
  "data": null
}
```

**Available Users**:

| Username   | Password | Role      | Permissions |
|------------|----------|-----------|-------------|
| admin      | 123456   | admin     | read, write, delete, approve, manage_users, view_all |
| headoffice | 123456   | headoffice| read, write, approve, view_branch |
| branch     | 123456   | branch    | read, write |
| thirdparty | 123456   | thirdparty| read |

### Get Current User Information

Returns current user information by username.

**URL**: `/api/users/current/{username}`

**Method**: `GET`

**URL Parameters**:
- `username`: Username

**Success Response**:
```json
{
  "code": 0,
  "message": "User information retrieved successfully",
  "data": {
    "id": 1,
    "username": "branch",
    "email": "<EMAIL>",
    "fullName": "分行操作员",
    "role": "branch",
    "roleTitle": "分行",
    "avatar": "",
    "department": "北京银行学XX分行",
    "status": 1,
    "createTime": "2023-06-01T10:30:00",
    "updateTime": "2023-06-01T10:30:00"
  }
}
```

**Error Response**:
```json
{
  "code": 1001,
  "message": "User not found",
  "data": null
}
```

## Exchange API

### Get All Exchanges

Returns a list of all exchanges.

**URL**: `/api/exchanges`

**Method**: `GET`

**Success Response**:
```json
{
  "code": 0,
  "message": "Exchanges retrieved successfully",
  "data": [
    {
      "id": 1,
      "sourceBank": "中国银行",
      "targetBank": "兴业银行XX支行",
      "exchangeRate": 1.01,
      "amount": 1000,
      "updateTime": "2021-02-28T10:30:00",
      "updater": "王岳 兴业银行XX分行"
    }
  ]
}
```

### Get Exchanges with Pagination and Filtering

Returns a paginated list of exchanges with optional filtering by source bank (开户行) and target bank (贴现行).

**URL**: `/api/exchanges/list`

**Method**: `GET`

**Query Parameters**:
- `pageNum` (optional): Page number (1-based, default: 1)
- `pageSize` (optional): Page size (default: 10)
- `sourceBank` (optional): Filter by source bank (开户行) - exact match
- `targetBank` (optional): Filter by target bank (贴现行) - fuzzy search (contains)

**Example**:
Searching with `targetBank=中国建设银行` will match records with target banks like:
- "中国建设银行XX支行"
- "建设银行XX分行"
- "XX市建设银行"
- Any other target bank containing "中国建设银行"

**Success Response**:
```json
{
  "code": 0,
  "message": "Exchanges retrieved successfully",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 100,
    "pages": 10,
    "list": [
      {
        "id": 1,
        "sourceBank": "中国银行",
        "targetBank": "兴业银行XX支行",
        "exchangeRate": 1.01,
        "amount": 1000,
        "updateTime": "2021-02-28T10:30:00",
        "updater": "王岳 兴业银行XX分行"
      }
    ],
    "hasPrevious": false,
    "hasNext": true
  }
}
```

### Get Exchanges for Current User (Branch Filter)

Returns a paginated list of exchanges filtered by current user's department. This is specifically designed for branch users who should only see exchanges related to their own branch.

**URL**: `/api/exchanges/list/current-user`

**Method**: `GET`

**Query Parameters**:
- `pageNum` (optional): Page number (1-based, default: 1)
- `pageSize` (optional): Page size (default: 10)
- `sourceBank` (optional): Filter by source bank (开户行) - exact match
- `targetBank` (optional): Filter by target bank (贴现行) - fuzzy search (contains)
- `currentUserDepartment` (required): Current user's department for filtering

**Example**:
For a branch user with department "北京银行XX分行", the system will only return exchanges where the target bank (贴现行) contains "北京银行XX分行".

**Request Example**:
```
GET /api/exchanges/list/current-user?pageNum=1&pageSize=10&currentUserDepartment=北京银行XX分行
```

**With additional filters**:
```
GET /api/exchanges/list/current-user?pageNum=1&pageSize=10&sourceBank=中国银行&currentUserDepartment=北京银行XX分行
```

**Success Response**:
```json
{
  "code": 0,
  "message": "Exchanges retrieved successfully for current user",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 5,
    "data": [
      {
        "id": 1,
        "sourceBank": "中国银行",
        "targetBank": "北京银行学XX分行",
        "exchangeRate": 1.01,
        "amount": 1000,
        "updateTime": "2021-02-28T10:30:00",
        "updater": "王岳 北京银行学XX分行"
      }
    ]
  }
}
```

### Get Exchange by ID

Returns a specific exchange by ID.

**URL**: `/api/exchanges/{id}`

**Method**: `GET`

**URL Parameters**:
- `id`: Exchange ID

**Success Response**:
```json
{
  "code": 0,
  "message": "Exchange retrieved successfully",
  "data": {
    "id": 1,
    "sourceBank": "中国银行",
    "targetBank": "兴业银行XX支行",
    "exchangeRate": 1.01,
    "amount": 1000,
    "updateTime": "2021-02-28T10:30:00",
    "updater": "王岳 兴业银行XX分行"
  }
}
```

**Error Response**:
```json
{
  "code": 2001,
  "message": "Exchange not found",
  "data": null
}
```

### Create Exchange

Creates a new exchange.

**URL**: `/api/exchanges`

**Method**: `POST`

**Request Body**:
```json
{
  "sourceBank": "中国银行",
  "targetBank": "兴业银行XX支行",
  "exchangeRate": 1.01,
  "amount": 1000,
  "updater": "王岳 兴业银行XX分行"
}
```

**Success Response**:
```json
{
  "code": 0,
  "message": "Exchange created successfully",
  "data": {
    "id": 101,
    "sourceBank": "中国银行",
    "targetBank": "兴业银行XX支行",
    "exchangeRate": 1.01,
    "amount": 1000,
    "updateTime": "2023-06-01T10:30:00",
    "updater": "王岳 兴业银行XX分行"
  }
}
```

**Error Response**:
```json
{
  "code": 2002,
  "message": "Failed to create exchange",
  "data": null
}
```

### Update Exchange

Updates an existing exchange.

**URL**: `/api/exchanges/{id}`

**Method**: `PUT`

**URL Parameters**:
- `id`: Exchange ID

**Request Body**:
```json
{
  "sourceBank": "中国银行",
  "targetBank": "兴业银行XX支行",
  "exchangeRate": 1.02,
  "amount": 1500,
  "updater": "王岳 兴业银行XX分行"
}
```

**Success Response**:
```json
{
  "code": 0,
  "message": "Exchange updated successfully",
  "data": {
    "id": 1,
    "sourceBank": "中国银行",
    "targetBank": "兴业银行XX支行",
    "exchangeRate": 1.02,
    "amount": 1500,
    "updateTime": "2023-06-01T11:30:00",
    "updater": "王岳 兴业银行XX分行"
  }
}
```

**Error Responses**:
```json
{
  "code": 2001,
  "message": "Exchange not found",
  "data": null
}
```

```json
{
  "code": 2003,
  "message": "Failed to update exchange",
  "data": null
}
```

### Batch Update Exchanges

Updates multiple exchanges at once, modifying only the exchange rate and amount fields.

**URL**: `/api/exchanges/batch`

**Method**: `PUT`

**Request Body**:
```json
{
  "exchanges": [
    {
      "id": 1,
      "exchangeRate": 1.02,
      "amount": 1500
    },
    {
      "id": 2,
      "exchangeRate": 0.99,
      "amount": 900
    }
  ],
  "updater": "王岳 兴业银行XX分行"
}
```

**Success Response**:
```json
{
  "code": 0,
  "message": "Exchanges batch updated successfully",
  "data": [
    {
      "id": 1,
      "sourceBank": "中国银行",
      "targetBank": "兴业银行XX支行",
      "exchangeRate": 1.02,
      "amount": 1500,
      "updateTime": "2023-06-01T11:30:00",
      "updater": "王岳 兴业银行XX分行"
    },
    {
      "id": 2,
      "sourceBank": "河北银行",
      "targetBank": "兴业银行XX支行",
      "exchangeRate": 0.99,
      "amount": 900,
      "updateTime": "2023-06-01T11:30:00",
      "updater": "王岳 兴业银行XX分行"
    }
  ]
}
```

**Note**: If an exchange with a specified ID is not found, it will be skipped in the batch update process.

### Delete Exchange

Deletes an exchange and optionally resequences the IDs in the database.

**URL**: `/api/exchanges/{id}`

**Method**: `DELETE`

**URL Parameters**:
- `id`: Exchange ID

**Query Parameters**:
- `resequence` (optional): Whether to resequence IDs after deletion (default: true)

**Success Response (with resequencing)**:
```json
{
  "code": 0,
  "message": "Exchange deleted and IDs resequenced successfully",
  "data": null
}
```

**Success Response (without resequencing)**:
```json
{
  "code": 0,
  "message": "Exchange deleted successfully",
  "data": null
}
```

**Error Responses**:
```json
{
  "code": 2001,
  "message": "Exchange not found",
  "data": null
}
```

```json
{
  "code": 2004,
  "message": "Failed to delete exchange",
  "data": null
}
```

## Operation Log API

操作日志记录系统中的增删改操作（不包括查询操作），包括以下操作类型：

- `create`: 创建新记录（包含贴现额度和贴现价格信息）
- `modify_贴现额度`: 修改贴现额度
- `modify_贴现价格`: 修改贴现价格
- `modify_贴现额度和贴现价格`: 同时修改贴现额度和贴现价格
- `delete`: 删除记录

日志中包含以下字段用于记录贴现额度和贴现价格：

- `oldValue`: 贴现额度
- `newValue`: 修改后的贴现额度
- `discountRate`: 贴现价格（创建操作和修改贴现额度操作时记录）
- `oldDiscountRate`: 修改前贴现价格（仅当修改贴现价格时有值）
- `newDiscountRate`: 修改后贴现价格（仅当修改贴现价格时有值）

### Get All Operation Logs

Returns a list of all operation logs.

**URL**: `/api/logs`

**Method**: `GET`

**Success Response**:
```json
{
  "code": 0,
  "message": "Operation logs retrieved successfully",
  "data": [
    {
      "id": 1,
      "operationTime": "2021-02-28T10:30:00",
      "operator": "王苗",
      "sourceBank": "兴业银行",
      "targetBank": "中国银行",
      "operationType": "modify_贴现额度",  // 操作类型: create, query, modify_贴现额度, modify_贴现价格, delete
      "oldValue": "800W",  // 贴现额度
      "newValue": "1000W",  // 修改后的贴现额度
      "discountRate": 1.01,  // 贴现价格
      "oldDiscountRate": null,  // 修改前贴现价格（仅当修改贴现价格时有值）
      "newDiscountRate": null   // 修改后贴现价格（仅当修改贴现价格时有值）
    },
    {
      "id": 2,
      "operationTime": "2021-02-28T10:30:00",
      "operator": "管理员",
      "sourceBank": "兴业银行",
      "targetBank": "农业银行",
      "operationType": "modify",
      "oldValue": "800W",
      "newValue": "1000W"
    },
    {
      "id": 3,
      "operationTime": "2021-02-28T10:30:00",
      "operator": "管理员",
      "sourceBank": "兴业银行",
      "targetBank": "中国银行",
      "operationType": "delete",
      "oldValue": null,
      "newValue": null
    }
  ]
}
```

### Get Operation Logs with Pagination

Returns a paginated list of operation logs.

**URL**: `/api/logs/list`

**Method**: `GET`

**Query Parameters**:
- `pageNum` (optional): Page number (1-based, default: 1)
- `pageSize` (optional): Page size (default: 10)

**Success Response**:
```json
{
  "code": 0,
  "message": "Operation logs retrieved successfully",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 3,
    "pages": 1,
    "list": [
      {
        "id": 1,
        "operationTime": "2021-02-28T10:30:00",
        "operator": "王苗",
        "sourceBank": "兴业银行",
        "targetBank": "中国银行",
        "operationType": "modify",
        "oldValue": "800W",
        "newValue": "1000W"
      },
      {
        "id": 2,
        "operationTime": "2021-02-28T10:30:00",
        "operator": "管理员",
        "sourceBank": "兴业银行",
        "targetBank": "农业银行",
        "operationType": "modify",
        "oldValue": "800W",
        "newValue": "1000W"
      },
      {
        "id": 3,
        "operationTime": "2021-02-28T10:30:00",
        "operator": "管理员",
        "sourceBank": "兴业银行",
        "targetBank": "中国银行",
        "operationType": "delete",
        "oldValue": null,
        "newValue": null
      }
    ],
    "hasPrevious": false,
    "hasNext": false
  }
}
```

## Test API

### Health Check

Checks if the application is running.

**URL**: `/api/test/health`

**Method**: `GET`

**Success Response**:
```json
{
  "status": "UP",
  "message": "Application is running"
}
```

Note: This endpoint does not follow the standard response format.
